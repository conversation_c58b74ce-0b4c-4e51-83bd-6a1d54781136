import numpy as np

# 代码转换工具
def export_forest_to_c(forest, num_trees):
    """
    将随机森林模型导出为C语言代码
    :param forest: 训练好的随机森林模型
    :param num_trees: 决策树数量
    """
    with open('forest.c', 'w') as f:
        # 写入头文件
        f.write('#include <stdio.h>\n')
        f.write('#include <string.h>\n')
        f.write('#include <stdint.h>\n')
        f.write('float predictTree(float features[], float cutPoint[], uint8_t cutPredictor[], uint16_t classProbability[], short children[][2], int nodeIndex);\n\n')

        # 为每棵树生成参数数据
        for t in range(1, num_trees + 1):
            tree = forest.Trees[t-1]
            nodes = tree.CutPoint
            cut_var = tree.CutPredictor
            class_prob = tree.ClassProbability
            children = tree.Children

            # 生成cutPoint数组
            cut_points = ['100' if np.isnan(x) else str(x) for x in nodes]
            f.write(f'const float cutPoint{t}[] = {{{", ".join(cut_points)}}};\n')

            # 生成cutPredictor数组
            predictors = []
            for var in cut_var:
                num = ''.join(filter(str.isdigit, var))
                predictors.append(num if num else '0')
            f.write(f'const uint8_t cutPredictor{t}[] = {{{", ".join(predictors)}}};\n')

            # 生成classProbability数组
            probs = [str(int(round(x * 10000))) for x in class_prob]
            f.write(f'const uint16_t classProbability{t}[] = {{{", ".join(probs)}}};\n')

            # 生成children数组
            child_str = []
            for left, right in children:
                child_str.append(f'{{{left},{right}}}')
            f.write(f'const short children{t}[][2] = {{{", ".join(child_str)}}};\n\n')

        # 主预测函数
        f.write('\nint predictForest(double features[]) {\n')
        f.write('    float resultClass = 0;\n')
        for t in range(1, num_trees + 1):
            f.write(f'    float class{t} = predictTree(features, cutPoint{t}, cutPredictor{t}, classProbability{t}, children{t}, 0);\n')
            f.write(f'    resultClass = resultClass + (class{t} - resultClass)/{t}.0;\n')
        
        f.write('    if(resultClass > 0.5){ \n')
        f.write('        return 0; \n')
        f.write('    }else{ \n')
        f.write('        return 1; }}\n')

        # 预测树函数
        f.write('\nfloat predictTree(float features[], float cutPoint[], uint8_t cutPredictor[], uint16_t classProbability[], short children[][2], int nodeIndex){ \n')
        f.write('   if(cutPoint[nodeIndex] == 100){ \n')
        f.write('       return classProbability[nodeIndex] / 10000.0;\n')
        f.write('   } \n')
        f.write('   else{ \n')
        f.write('       if(features[cutPredictor[nodeIndex] - 1] < cutPoint[nodeIndex]){ \n')
        f.write('           predictTree(features, cutPoint, cutPredictor, classProbability, children, children[nodeIndex][0] -1);\n')
        f.write('        }else{\n')
        f.write('           predictTree(features, cutPoint, cutPredictor, classProbability, children, children[nodeIndex][1] -1);\n')
        f.write('  }}} \n')