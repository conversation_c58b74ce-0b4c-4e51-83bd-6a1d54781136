import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import os
from scipy import signal
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# 导入fft_random_forest中的函数
from fft_random_forest import data_pre_process, load_csv_robust, scan_dir

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_csv_robust(file_path):
    """
    健壮的CSV文件加载函数，能够处理包含引号和其他特殊字符的数据
    :param file_path: CSV文件路径
    :return: 数值数组
    """
    try:
        # 首先尝试使用pandas读取CSV文件
        df = pd.read_csv(file_path, header=None, dtype=str)
        
        # 如果只有一列数据，将其转换为一维数组
        if df.shape[1] == 1:
            data = df.iloc[:, 0].values
        else:
            # 如果有多列，取第一列或者展平所有数据
            data = df.values.flatten()
        
        # 清理数据：移除引号和其他非数字字符
        cleaned_data = []
        for item in data:
            if pd.isna(item):
                continue
            # 转换为字符串并清理
            item_str = str(item).strip()
            # 移除引号
            item_str = item_str.replace('"', '').replace("'", '')
            # 尝试转换为浮点数
            try:
                cleaned_data.append(float(item_str))
            except ValueError:
                # 如果无法转换为数字，跳过这个值
                continue
        
        return np.array(cleaned_data)
        
    except Exception:
        # 如果pandas失败，尝试使用numpy的genfromtxt
        try:
            # 使用genfromtxt，它对格式更宽容
            data = np.genfromtxt(file_path, delimiter=',', invalid_raise=False, 
                               converters={i: lambda s: float(s.decode().replace('"', '').replace("'", '')) 
                                         if s else np.nan for i in range(10)})
            # 移除NaN值
            data = data[~np.isnan(data)]
            return data
        except Exception:
            # 最后尝试逐行读取文件
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                cleaned_data = []
                for line in lines:
                    # 分割行并清理每个值
                    values = line.strip().split(',')
                    for val in values:
                        val = val.strip().replace('"', '').replace("'", '')
                        try:
                            cleaned_data.append(float(val))
                        except ValueError:
                            continue
                
                return np.array(cleaned_data)
            except Exception as e3:
                raise ValueError(f"无法加载文件 {file_path}: {e3}")

def scan_dir(root_dir):
    """
    递归扫描目录下的所有文件
    :param root_dir: 根目录路径
    :return: 文件路径列表
    """
    files = []
    for dirpath, _, filenames in os.walk(root_dir):
        for filename in filenames:
            if filename.endswith('.csv'):
                full_path = os.path.join(dirpath, filename)
                files.append(full_path)
    return files

def perform_fft_analysis(data, fs=250000):
    """
    对数据进行FFT分析
    :param data: 输入数据
    :param fs: 采样频率
    :return: 频率数组和FFT幅度
    """
    # 计算FFT
    fft_data = np.fft.fft(data)
    fft_magnitude = np.abs(fft_data)
    
    # 计算频率数组
    n = len(data)
    freq = np.fft.fftfreq(n, 1/fs)
    
    # 只取正频率部分
    positive_freq_idx = freq >= 0
    freq_positive = freq[positive_freq_idx]
    magnitude_positive = fft_magnitude[positive_freq_idx]
    
    return freq_positive, magnitude_positive

def create_time_domain_plot(data, fs=250000, title="时域信号"):
    """
    创建时域信号图
    :param data: 信号数据
    :param fs: 采样频率
    :param title: 图表标题
    :return: matplotlib figure对象
    """
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # 计算时间轴
    t = np.arange(len(data)) / fs
    
    ax.plot(t, data, 'b-', linewidth=0.8)
    ax.set_xlabel('时间 (s)')
    ax.set_ylabel('幅度')
    ax.set_title(title)
    ax.grid(True, alpha=0.3)
    
    return fig

def create_fft_plot(freq, magnitude, title="频域分析"):
    """
    创建FFT频域图
    :param freq: 频率数组
    :param magnitude: FFT幅度
    :param title: 图表标题
    :return: matplotlib figure对象
    """
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # 线性幅度图
    ax1.plot(freq, magnitude, 'r-', linewidth=0.8)
    ax1.set_xlabel('频率 (Hz)')
    ax1.set_ylabel('幅度')
    ax1.set_title(f'{title} - 线性幅度')
    ax1.grid(True, alpha=0.3)
    ax1.set_xlim(0, min(50000, max(freq)))  # 限制显示到50kHz
    
    # 对数幅度图
    ax2.semilogy(freq, magnitude, 'g-', linewidth=0.8)
    ax2.set_xlabel('频率 (Hz)')
    ax2.set_ylabel('幅度 (对数)')
    ax2.set_title(f'{title} - 对数幅度')
    ax2.grid(True, alpha=0.3)
    ax2.set_xlim(0, min(50000, max(freq)))  # 限制显示到50kHz
    
    plt.tight_layout()
    return fig

def create_spectrogram(data, fs=250000, title="频谱图"):
    """
    创建频谱图
    :param data: 信号数据
    :param fs: 采样频率
    :param title: 图表标题
    :return: matplotlib figure对象
    """
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 计算频谱图
    f, t, Sxx = signal.spectrogram(data, fs, nperseg=1024, noverlap=512)
    
    # 绘制频谱图
    im = ax.pcolormesh(t, f, 10 * np.log10(Sxx), shading='gouraud', cmap='viridis')
    ax.set_ylabel('频率 (Hz)')
    ax.set_xlabel('时间 (s)')
    ax.set_title(title)
    ax.set_ylim(0, min(50000, max(f)))  # 限制显示到50kHz
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax)
    cbar.set_label('功率谱密度 (dB/Hz)')
    
    return fig

def process_and_visualize_data(data_path, output_dir, data_type="ARC"):
    """
    处理并可视化数据
    :param data_path: 数据路径
    :param output_dir: 输出目录
    :param data_type: 数据类型 ("ARC" 或 "NOR")
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 扫描文件
    files = scan_dir(data_path)
    print(f"找到 {len(files)} 个 {data_type} 文件")
    
    if not files:
        print(f"在 {data_path} 中未找到CSV文件")
        return
    
    # 处理每个文件
    for i, file_path in enumerate(tqdm(files[:10], desc=f"处理{data_type}数据")):  # 限制处理前10个文件
        try:
            # 加载数据
            data = load_csv_robust(file_path)
            
            if len(data) == 0:
                print(f"文件 {file_path} 数据为空，跳过")
                continue
            
            # 获取文件名（不含扩展名）
            filename = os.path.splitext(os.path.basename(file_path))[0]
            
            # 进行FFT分析
            freq, magnitude = perform_fft_analysis(data)
            
            # 创建时域图
            time_fig = create_time_domain_plot(data, title=f"{data_type} - {filename} - 时域信号")
            time_output_path = os.path.join(output_dir, f"{data_type}_{filename}_time_domain.png")
            time_fig.savefig(time_output_path, dpi=300, bbox_inches='tight')
            plt.close(time_fig)
            
            # 创建FFT图
            fft_fig = create_fft_plot(freq, magnitude, title=f"{data_type} - {filename}")
            fft_output_path = os.path.join(output_dir, f"{data_type}_{filename}_fft.png")
            fft_fig.savefig(fft_output_path, dpi=300, bbox_inches='tight')
            plt.close(fft_fig)
            
            # 创建频谱图（仅对较短的数据创建，避免内存问题）
            if len(data) <= 100000:  # 限制数据长度
                spec_fig = create_spectrogram(data, title=f"{data_type} - {filename} - 频谱图")
                spec_output_path = os.path.join(output_dir, f"{data_type}_{filename}_spectrogram.png")
                spec_fig.savefig(spec_output_path, dpi=300, bbox_inches='tight')
                plt.close(spec_fig)
            
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            continue

def visualize_processed_features(feature_matrix, labels, arc_length, norm_length, output_dir, average_width=5, save_individual=True):
    """
    可视化data_pre_process处理后的特征数据
    :param feature_matrix: 特征矩阵 (n_samples, n_features)
    :param labels: 标签数组
    :param arc_length: 故障样本数量
    :param norm_length: 正常样本数量
    :param output_dir: 输出目录
    :param average_width: 平均窗口宽度
    :param save_individual: 是否保存每个样本的单独图表
    """
    os.makedirs(output_dir, exist_ok=True)

    # 分离ARC和Normal特征
    arc_features = feature_matrix[:arc_length]
    norm_features = feature_matrix[arc_length:]

    print("生成汇总分析图表...")

    # 1. 特征分布对比图
    create_feature_distribution_plot(arc_features, norm_features, output_dir)

    # 2. 特征均值对比图
    create_feature_mean_comparison(arc_features, norm_features, output_dir, average_width)

    # 3. 特征热力图
    create_feature_heatmap(arc_features, norm_features, output_dir)

    # 4. 主成分分析可视化
    create_pca_visualization(feature_matrix, labels, output_dir)

    # 5. 样本特征曲线图
    create_sample_feature_curves(arc_features, norm_features, output_dir)

    # 6. 样本汇总图表
    create_sample_summary_plots(feature_matrix, labels, arc_length, norm_length, output_dir)

    # 7. 每个样本的单独图表（可选）
    if save_individual:
        print("生成每个样本的单独图表...")
        save_individual_sample_plots(feature_matrix, labels, arc_length, norm_length, output_dir)
    else:
        print("跳过单独样本图表生成（save_individual=False）")

def create_feature_distribution_plot(arc_features, norm_features, output_dir):
    """
    创建特征分布对比图
    """
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))

    # 计算统计信息
    arc_mean = np.mean(arc_features, axis=0)
    norm_mean = np.mean(norm_features, axis=0)
    arc_std = np.std(arc_features, axis=0)
    norm_std = np.std(norm_features, axis=0)

    # 特征索引
    feature_idx = np.arange(len(arc_mean))

    # 1. 均值对比
    axes[0, 0].plot(feature_idx, arc_mean, 'r-', label='ARC (故障)', linewidth=2)
    axes[0, 0].plot(feature_idx, norm_mean, 'b-', label='Normal (正常)', linewidth=2)
    axes[0, 0].set_title('特征均值对比')
    axes[0, 0].set_xlabel('特征索引')
    axes[0, 0].set_ylabel('特征值')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 2. 标准差对比
    axes[0, 1].plot(feature_idx, arc_std, 'r-', label='ARC (故障)', linewidth=2)
    axes[0, 1].plot(feature_idx, norm_std, 'b-', label='Normal (正常)', linewidth=2)
    axes[0, 1].set_title('特征标准差对比')
    axes[0, 1].set_xlabel('特征索引')
    axes[0, 1].set_ylabel('标准差')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # 3. 特征差异
    feature_diff = arc_mean - norm_mean
    axes[1, 0].bar(feature_idx, feature_diff, color=['red' if x > 0 else 'blue' for x in feature_diff])
    axes[1, 0].set_title('特征差异 (ARC - Normal)')
    axes[1, 0].set_xlabel('特征索引')
    axes[1, 0].set_ylabel('差异值')
    axes[1, 0].grid(True, alpha=0.3)

    # 4. 箱线图对比（选择前10个特征）
    selected_features = min(10, len(arc_mean))
    box_data = []
    box_labels = []
    for i in range(selected_features):
        box_data.extend([arc_features[:, i], norm_features[:, i]])
        box_labels.extend([f'ARC-{i}', f'NOR-{i}'])

    bp = axes[1, 1].boxplot(box_data, labels=box_labels, patch_artist=True)
    # 设置颜色
    for i, patch in enumerate(bp['boxes']):
        if i % 2 == 0:  # ARC
            patch.set_facecolor('red')
            patch.set_alpha(0.6)
        else:  # Normal
            patch.set_facecolor('blue')
            patch.set_alpha(0.6)

    axes[1, 1].set_title(f'前{selected_features}个特征的分布对比')
    axes[1, 1].set_xlabel('特征')
    axes[1, 1].set_ylabel('特征值')
    axes[1, 1].tick_params(axis='x', rotation=45)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'feature_distribution_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close(fig)

def create_feature_mean_comparison(arc_features, norm_features, output_dir, average_width):
    """
    创建特征均值对比图，显示频率域信息
    """
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

    arc_mean = np.mean(arc_features, axis=0)
    norm_mean = np.mean(norm_features, axis=0)

    # 计算对应的频率
    # 基于mystft函数的处理逻辑
    fs = 1 / 4e-6  # 采样频率
    fft_length = 512
    freq_resolution = fs / (2 * fft_length)  # 频率分辨率

    # 每个特征对应的频率范围
    freq_bins = []
    for i in range(len(arc_mean)):
        start_freq = i * average_width * freq_resolution
        end_freq = (i + 1) * average_width * freq_resolution
        freq_bins.append((start_freq + end_freq) / 2)  # 中心频率

    freq_bins = np.array(freq_bins)

    # 线性尺度
    ax1.plot(freq_bins, arc_mean, 'r-', label='ARC (故障)', linewidth=2, marker='o', markersize=3)
    ax1.plot(freq_bins, norm_mean, 'b-', label='Normal (正常)', linewidth=2, marker='s', markersize=3)
    ax1.set_title('特征均值对比 - 线性尺度')
    ax1.set_xlabel('频率 (Hz)')
    ax1.set_ylabel('特征值')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 对数尺度
    ax2.semilogy(freq_bins, arc_mean, 'r-', label='ARC (故障)', linewidth=2, marker='o', markersize=3)
    ax2.semilogy(freq_bins, norm_mean, 'b-', label='Normal (正常)', linewidth=2, marker='s', markersize=3)
    ax2.set_title('特征均值对比 - 对数尺度')
    ax2.set_xlabel('频率 (Hz)')
    ax2.set_ylabel('特征值 (对数)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'feature_frequency_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close(fig)

def create_feature_heatmap(arc_features, norm_features, output_dir):
    """
    创建特征热力图
    """
    fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6))

    # 1. ARC特征热力图
    im1 = ax1.imshow(arc_features[:50].T, aspect='auto', cmap='Reds', interpolation='nearest')
    ax1.set_title('ARC (故障) 特征热力图')
    ax1.set_xlabel('样本索引')
    ax1.set_ylabel('特征索引')
    plt.colorbar(im1, ax=ax1)

    # 2. Normal特征热力图
    im2 = ax2.imshow(norm_features[:50].T, aspect='auto', cmap='Blues', interpolation='nearest')
    ax2.set_title('Normal (正常) 特征热力图')
    ax2.set_xlabel('样本索引')
    ax2.set_ylabel('特征索引')
    plt.colorbar(im2, ax=ax2)

    # 3. 差异热力图
    arc_mean = np.mean(arc_features, axis=0).reshape(-1, 1)
    norm_mean = np.mean(norm_features, axis=0).reshape(-1, 1)
    diff_map = arc_mean - norm_mean

    im3 = ax3.imshow(diff_map, aspect='auto', cmap='RdBu_r', interpolation='nearest')
    ax3.set_title('特征差异热力图 (ARC - Normal)')
    ax3.set_xlabel('差异值')
    ax3.set_ylabel('特征索引')
    plt.colorbar(im3, ax=ax3)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'feature_heatmap.png'), dpi=300, bbox_inches='tight')
    plt.close(fig)

def create_pca_visualization(feature_matrix, labels, output_dir):
    """
    创建主成分分析可视化
    """
    try:
        from sklearn.decomposition import PCA
        from sklearn.manifold import TSNE

        # PCA分析
        pca = PCA(n_components=2)
        pca_result = pca.fit_transform(feature_matrix)

        # t-SNE分析（如果样本数不太多）
        if len(feature_matrix) <= 1000:
            tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, len(feature_matrix)//4))
            tsne_result = tsne.fit_transform(feature_matrix)
        else:
            tsne_result = None

        # 创建图表
        if tsne_result is not None:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        else:
            fig, ax1 = plt.subplots(1, 1, figsize=(8, 6))

        # PCA可视化
        arc_mask = labels == 1
        norm_mask = labels == 0

        ax1.scatter(pca_result[norm_mask, 0], pca_result[norm_mask, 1],
                   c='blue', alpha=0.6, label='Normal', s=20)
        ax1.scatter(pca_result[arc_mask, 0], pca_result[arc_mask, 1],
                   c='red', alpha=0.6, label='ARC', s=20)
        ax1.set_title(f'PCA可视化\n(解释方差比: PC1={pca.explained_variance_ratio_[0]:.3f}, PC2={pca.explained_variance_ratio_[1]:.3f})')
        ax1.set_xlabel('第一主成分')
        ax1.set_ylabel('第二主成分')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # t-SNE可视化
        if tsne_result is not None:
            ax2.scatter(tsne_result[norm_mask, 0], tsne_result[norm_mask, 1],
                       c='blue', alpha=0.6, label='Normal', s=20)
            ax2.scatter(tsne_result[arc_mask, 0], tsne_result[arc_mask, 1],
                       c='red', alpha=0.6, label='ARC', s=20)
            ax2.set_title('t-SNE可视化')
            ax2.set_xlabel('t-SNE 1')
            ax2.set_ylabel('t-SNE 2')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'pca_visualization.png'), dpi=300, bbox_inches='tight')
        plt.close(fig)

    except ImportError:
        print("警告: sklearn未安装，跳过PCA可视化")

def create_sample_feature_curves(arc_features, norm_features, output_dir):
    """
    创建样本特征曲线图
    """
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

    # 随机选择一些样本进行可视化
    n_samples_to_show = min(20, len(arc_features), len(norm_features))

    # ARC样本特征曲线
    arc_indices = np.random.choice(len(arc_features), n_samples_to_show, replace=False)
    for i, idx in enumerate(arc_indices):
        alpha = 0.7 if i < 5 else 0.3  # 前5个样本更明显
        ax1.plot(arc_features[idx], color='red', alpha=alpha, linewidth=1)

    # 添加均值曲线
    ax1.plot(np.mean(arc_features, axis=0), color='darkred', linewidth=3, label='ARC均值')
    ax1.set_title(f'ARC (故障) 样本特征曲线 (显示{n_samples_to_show}个样本)')
    ax1.set_xlabel('特征索引')
    ax1.set_ylabel('特征值')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Normal样本特征曲线
    norm_indices = np.random.choice(len(norm_features), n_samples_to_show, replace=False)
    for i, idx in enumerate(norm_indices):
        alpha = 0.7 if i < 5 else 0.3  # 前5个样本更明显
        ax2.plot(norm_features[idx], color='blue', alpha=alpha, linewidth=1)

    # 添加均值曲线
    ax2.plot(np.mean(norm_features, axis=0), color='darkblue', linewidth=3, label='Normal均值')
    ax2.set_title(f'Normal (正常) 样本特征曲线 (显示{n_samples_to_show}个样本)')
    ax2.set_xlabel('特征索引')
    ax2.set_ylabel('特征值')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'sample_feature_curves.png'), dpi=300, bbox_inches='tight')
    plt.close(fig)

def save_individual_sample_plots(feature_matrix, labels, arc_length, norm_length, output_dir):
    """
    为每个样本生成单独的特征折线图并保存
    :param feature_matrix: 特征矩阵 (n_samples, n_features)
    :param labels: 标签数组
    :param arc_length: 故障样本数量
    :param norm_length: 正常样本数量
    :param output_dir: 输出目录
    """
    # 创建子目录
    arc_dir = os.path.join(output_dir, "individual_samples", "ARC")
    norm_dir = os.path.join(output_dir, "individual_samples", "NOR")
    os.makedirs(arc_dir, exist_ok=True)
    os.makedirs(norm_dir, exist_ok=True)

    print(f"开始生成每个样本的特征折线图...")
    print(f"ARC样本数: {arc_length}, Normal样本数: {norm_length}")

    # 特征索引
    feature_indices = np.arange(feature_matrix.shape[1])

    # 处理ARC样本
    print("生成ARC样本图表...")
    for i in tqdm(range(arc_length), desc="ARC样本"):
        fig, ax = plt.subplots(figsize=(12, 6))

        sample_features = feature_matrix[i]
        ax.plot(feature_indices, sample_features, 'r-', linewidth=2, marker='o', markersize=3)
        ax.set_title(f'ARC样本 #{i+1} 特征曲线')
        ax.set_xlabel('特征索引')
        ax.set_ylabel('特征值')
        ax.grid(True, alpha=0.3)

        # 添加统计信息
        mean_val = np.mean(sample_features)
        max_val = np.max(sample_features)
        min_val = np.min(sample_features)
        ax.text(0.02, 0.98, f'均值: {mean_val:.4f}\n最大值: {max_val:.4f}\n最小值: {min_val:.4f}',
                transform=ax.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        plt.savefig(os.path.join(arc_dir, f'ARC_sample_{i+1:03d}.png'), dpi=300, bbox_inches='tight')
        plt.close(fig)

    # 处理Normal样本
    print("生成Normal样本图表...")
    for i in tqdm(range(norm_length), desc="Normal样本"):
        fig, ax = plt.subplots(figsize=(12, 6))

        sample_features = feature_matrix[arc_length + i]
        ax.plot(feature_indices, sample_features, 'b-', linewidth=2, marker='s', markersize=3)
        ax.set_title(f'Normal样本 #{i+1} 特征曲线')
        ax.set_xlabel('特征索引')
        ax.set_ylabel('特征值')
        ax.grid(True, alpha=0.3)

        # 添加统计信息
        mean_val = np.mean(sample_features)
        max_val = np.max(sample_features)
        min_val = np.min(sample_features)
        ax.text(0.02, 0.98, f'均值: {mean_val:.4f}\n最大值: {max_val:.4f}\n最小值: {min_val:.4f}',
                transform=ax.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        plt.savefig(os.path.join(norm_dir, f'Normal_sample_{i+1:03d}.png'), dpi=300, bbox_inches='tight')
        plt.close(fig)

    print(f"个体样本图表生成完成！")
    print(f"ARC样本图表保存在: {arc_dir}")
    print(f"Normal样本图表保存在: {norm_dir}")

def create_sample_summary_plots(feature_matrix, labels, arc_length, norm_length, output_dir):
    """
    创建样本特征的汇总图表
    """
    # 分离数据
    arc_features = feature_matrix[:arc_length]
    norm_features = feature_matrix[arc_length:]

    # 1. 创建网格图显示多个样本
    fig, axes = plt.subplots(4, 5, figsize=(20, 16))
    fig.suptitle('样本特征曲线汇总 (前20个样本)', fontsize=16)

    feature_indices = np.arange(feature_matrix.shape[1])

    for i in range(20):
        row = i // 5
        col = i % 5
        ax = axes[row, col]

        if i < 10 and i < arc_length:
            # 显示ARC样本
            sample_features = arc_features[i]
            ax.plot(feature_indices, sample_features, 'r-', linewidth=1.5, alpha=0.8)
            ax.set_title(f'ARC #{i+1}', fontsize=10)
            ax.set_facecolor('#fff5f5')
        elif i >= 10 and (i-10) < norm_length:
            # 显示Normal样本
            sample_features = norm_features[i-10]
            ax.plot(feature_indices, sample_features, 'b-', linewidth=1.5, alpha=0.8)
            ax.set_title(f'Normal #{i-9}', fontsize=10)
            ax.set_facecolor('#f5f5ff')
        else:
            ax.set_visible(False)
            continue

        ax.set_xlabel('特征索引', fontsize=8)
        ax.set_ylabel('特征值', fontsize=8)
        ax.grid(True, alpha=0.3)
        ax.tick_params(labelsize=8)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'sample_summary_grid.png'), dpi=300, bbox_inches='tight')
    plt.close(fig)

    # 2. 创建叠加图显示所有样本
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))

    # ARC样本叠加图
    for i in range(min(50, arc_length)):  # 最多显示50个样本
        alpha = 0.1 if i >= 10 else 0.3
        ax1.plot(feature_indices, arc_features[i], 'r-', alpha=alpha, linewidth=0.8)

    # 添加均值线
    arc_mean = np.mean(arc_features, axis=0)
    ax1.plot(feature_indices, arc_mean, 'darkred', linewidth=3, label='ARC均值')
    ax1.fill_between(feature_indices,
                     arc_mean - np.std(arc_features, axis=0),
                     arc_mean + np.std(arc_features, axis=0),
                     alpha=0.2, color='red', label='±1标准差')

    ax1.set_title(f'ARC样本特征分布 (显示{min(50, arc_length)}个样本)')
    ax1.set_xlabel('特征索引')
    ax1.set_ylabel('特征值')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Normal样本叠加图
    for i in range(min(50, norm_length)):  # 最多显示50个样本
        alpha = 0.1 if i >= 10 else 0.3
        ax2.plot(feature_indices, norm_features[i], 'b-', alpha=alpha, linewidth=0.8)

    # 添加均值线
    norm_mean = np.mean(norm_features, axis=0)
    ax2.plot(feature_indices, norm_mean, 'darkblue', linewidth=3, label='Normal均值')
    ax2.fill_between(feature_indices,
                     norm_mean - np.std(norm_features, axis=0),
                     norm_mean + np.std(norm_features, axis=0),
                     alpha=0.2, color='blue', label='±1标准差')

    ax2.set_title(f'Normal样本特征分布 (显示{min(50, norm_length)}个样本)')
    ax2.set_xlabel('特征索引')
    ax2.set_ylabel('特征值')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'sample_overlay_plots.png'), dpi=300, bbox_inches='tight')
    plt.close(fig)

def main():
    """
    主函数 - 使用data_pre_process方法处理数据并可视化
    """
    # 数据路径
    test_arc_path = r"D:\AFCI\测试\ARC\工况1\机型1\2A-3A\ARC"
    test_normal_path = r"D:\Dev\AFCI云边协同\数据\processed\NOR"

    # 输出目录
    output_dir = "results/fft_vis_processed_trained_data"

    # 参数设置（与fft_random_forest.py保持一致）
    average_width = 5
    feature_length = int(512 / average_width)

    print("开始使用data_pre_process方法进行FFT可视化分析...")
    print(f"输出目录: {output_dir}")
    print(f"平均窗口宽度: {average_width}")
    print(f"特征长度: {feature_length}")

    try:
        # 使用data_pre_process处理数据
        feature_matrix, labels, arc_length, norm_length = data_pre_process(
            test_arc_path, test_normal_path, average_width, feature_length
        )

        print(f"\n数据处理完成:")
        print(f"特征矩阵形状: {feature_matrix.shape}")
        print(f"故障样本数: {arc_length}")
        print(f"正常样本数: {norm_length}")

        # 可视化处理后的特征
        print("\n开始生成可视化图表...")
        visualize_processed_features(feature_matrix, labels, arc_length, norm_length, output_dir, average_width)

        print(f"\n可视化完成！结果保存在 {output_dir} 目录下")

    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def main_original():
    """
    原始的主函数 - 直接处理原始数据
    """
    # 数据路径
    test_arc_path = r"D:\Dev\AFCI云边协同\数据\processed\ARC"
    test_normal_path = r"D:\Dev\AFCI云边协同\数据\processed\NOR"

    # 输出目录
    output_dir = "results/fft_vis_original"

    print("开始原始FFT可视化分析...")
    print(f"输出目录: {output_dir}")

    # 处理ARC数据
    if os.path.exists(test_arc_path):
        print("\n处理ARC（故障）数据...")
        process_and_visualize_data(test_arc_path, output_dir, "ARC")
    else:
        print(f"ARC数据路径不存在: {test_arc_path}")

    # 处理Normal数据
    if os.path.exists(test_normal_path):
        print("\n处理Normal（正常）数据...")
        process_and_visualize_data(test_normal_path, output_dir, "NOR")
    else:
        print(f"Normal数据路径不存在: {test_normal_path}")

    print(f"\n可视化完成！结果保存在 {output_dir} 目录下")

if __name__ == "__main__":
    main()
