"""
测试修改后的数据分段逻辑
验证当数据长度不能完整分割为1024长度的段时，最后一段向前补齐的功能
"""
import numpy as np
import os
import sys

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_segment_logic():
    """
    测试分段逻辑
    """
    print("=" * 60)
    print("测试数据分段逻辑")
    print("=" * 60)
    
    wlen = 1024  # 窗口长度
    
    # 测试用例
    test_cases = [
        {"length": 2000, "description": "长度2000，应分为2段：0-1023, 976-1999"},
        {"length": 1024, "description": "长度1024，应分为1段：0-1023"},
        {"length": 1500, "description": "长度1500，应分为2段：0-1023, 476-1499"},
        {"length": 3072, "description": "长度3072，应分为3段：0-1023, 1024-2047, 2048-3071"},
        {"length": 3100, "description": "长度3100，应分为4段：0-1023, 1024-2047, 2048-3071, 2076-3099"},
        {"length": 500, "description": "长度500，小于窗口长度，应跳过"},
    ]
    
    print("分段逻辑测试:")
    print(f"窗口长度: {wlen}")
    print()
    
    for case in test_cases:
        length_yk = case["length"]
        description = case["description"]
        
        print(f"测试用例: {description}")
        print(f"数据长度: {length_yk}")
        
        if length_yk < wlen:
            num_segments = 0
            print(f"结果: 跳过（数据长度小于窗口长度）")
        else:
            # 计算完整段数
            full_segments = length_yk // wlen
            # 检查是否有剩余数据
            remainder = length_yk % wlen
            if remainder > 0:
                # 有剩余数据，增加一段（向前补齐）
                num_segments = full_segments + 1
            else:
                # 无剩余数据，只有完整段
                num_segments = full_segments
            
            print(f"完整段数: {full_segments}")
            print(f"剩余数据: {remainder}")
            print(f"总段数: {num_segments}")
            
            # 显示每段的索引范围
            print("各段索引范围:")
            for i in range(num_segments):
                if i < length_yk // wlen:
                    # 完整段：正常分割
                    start_idx = i * wlen
                    end_idx = (i + 1) * wlen - 1
                    print(f"  段{i+1}: {start_idx}-{end_idx} (完整段)")
                else:
                    # 最后一段：向前补齐
                    start_idx = length_yk - wlen
                    end_idx = length_yk - 1
                    print(f"  段{i+1}: {start_idx}-{end_idx} (向前补齐)")
        
        print("-" * 40)

def create_test_data_and_verify():
    """
    创建测试数据并验证分段功能
    """
    print("\n" + "=" * 60)
    print("创建测试数据并验证分段功能")
    print("=" * 60)
    
    # 创建测试目录
    test_dir = "test_segment_data"
    arc_dir = os.path.join(test_dir, "ARC")
    nor_dir = os.path.join(test_dir, "NOR")
    
    os.makedirs(arc_dir, exist_ok=True)
    os.makedirs(nor_dir, exist_ok=True)
    
    # 创建不同长度的测试数据
    test_lengths = [2000, 1500, 3100]
    
    for i, length in enumerate(test_lengths):
        # 生成测试信号
        t = np.linspace(0, length/250000, length)  # 假设采样频率250kHz
        signal = np.sin(2 * np.pi * 50 * t) + 0.1 * np.random.randn(length)
        
        # 保存ARC和Normal数据
        np.savetxt(os.path.join(arc_dir, f"test_arc_{length}.csv"), signal, delimiter=',')
        np.savetxt(os.path.join(nor_dir, f"test_normal_{length}.csv"), signal, delimiter=',')
        
        print(f"创建测试文件: 长度{length}")
    
    print(f"\n测试数据已创建在 {test_dir} 目录下")
    
    # 测试data_pre_process函数
    try:
        from fft_random_forest import data_pre_process
        
        print("\n开始测试data_pre_process函数...")
        
        # 参数设置
        average_width = 5
        feature_length = int(512 / average_width)
        
        # 运行data_pre_process
        feature_matrix, labels, arc_length, norm_length = data_pre_process(
            arc_dir, nor_dir, average_width, feature_length
        )
        
        print(f"\ndata_pre_process测试结果:")
        print(f"  特征矩阵形状: {feature_matrix.shape}")
        print(f"  故障样本数: {arc_length}")
        print(f"  正常样本数: {norm_length}")
        print(f"  总样本数: {len(labels)}")
        
        # 验证分段数量
        expected_segments = 0
        for length in test_lengths:
            wlen = 1024
            if length >= wlen:
                full_segments = length // wlen
                remainder = length % wlen
                if remainder > 0:
                    expected_segments += full_segments + 1
                else:
                    expected_segments += full_segments
        
        expected_total = expected_segments * 2  # ARC + Normal
        actual_total = arc_length + norm_length
        
        print(f"\n分段验证:")
        print(f"  预期总段数: {expected_total}")
        print(f"  实际总段数: {actual_total}")
        
        if expected_total == actual_total:
            print("  ✅ 分段逻辑验证通过！")
        else:
            print("  ❌ 分段逻辑验证失败！")
        
        return True
        
    except Exception as e:
        print(f"\ndata_pre_process测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试数据
        import shutil
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            print(f"\n已清理测试目录: {test_dir}")

def demonstrate_example():
    """
    演示具体的例子：长度2000的数据分段
    """
    print("\n" + "=" * 60)
    print("演示例子：长度2000的数据分段")
    print("=" * 60)
    
    length_yk = 2000
    wlen = 1024
    
    print(f"数据长度: {length_yk}")
    print(f"窗口长度: {wlen}")
    
    # 计算分段
    full_segments = length_yk // wlen  # 2000 // 1024 = 1
    remainder = length_yk % wlen       # 2000 % 1024 = 976
    
    print(f"完整段数: {full_segments}")
    print(f"剩余数据长度: {remainder}")
    
    if remainder > 0:
        num_segments = full_segments + 1  # 1 + 1 = 2
    else:
        num_segments = full_segments
    
    print(f"总段数: {num_segments}")
    print()
    
    # 模拟数据分段
    data = np.arange(length_yk)  # 创建0到1999的数据
    
    print("分段结果:")
    for i in range(num_segments):
        if i < length_yk // wlen:
            # 完整段：正常分割
            start_idx = i * wlen
            end_idx = (i + 1) * wlen
            segment = data[start_idx:end_idx]
            print(f"段{i+1}: 索引{start_idx}-{end_idx-1} (长度{len(segment)})")
            print(f"      数据范围: {segment[0]}-{segment[-1]}")
        else:
            # 最后一段：向前补齐
            start_idx = length_yk - wlen  # 2000 - 1024 = 976
            end_idx = length_yk           # 2000
            segment = data[start_idx:end_idx]
            print(f"段{i+1}: 索引{start_idx}-{end_idx-1} (长度{len(segment)}) [向前补齐]")
            print(f"      数据范围: {segment[0]}-{segment[-1]}")
    
    print(f"\n说明:")
    print(f"- 第1段: 0-1023 (正常的完整段)")
    print(f"- 第2段: 976-1999 (向前补齐，包含了976-1023的重叠部分)")
    print(f"- 重叠部分: 976-1023 (48个数据点)")

def main():
    """
    主测试函数
    """
    print("开始测试修改后的数据分段逻辑...")
    
    # 测试1: 理论分段逻辑
    test_segment_logic()
    
    # 测试2: 具体例子演示
    demonstrate_example()
    
    # 测试3: 实际功能验证
    success = create_test_data_and_verify()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if success:
        print("✅ 所有测试都通过了！")
        print("✅ 数据分段逻辑修改成功")
        print("✅ 最后一段数据不再丢弃，而是向前补齐")
    else:
        print("❌ 有测试失败，请检查代码修改")

if __name__ == "__main__":
    main()
