"""
测试使用data_pre_process方法的FFT可视化功能
"""
import os
import sys
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_data_for_processing():
    """
    创建用于data_pre_process的测试数据
    """
    # 创建测试目录
    test_dir = "test_data_processed"
    arc_dir = os.path.join(test_dir, "ARC")
    nor_dir = os.path.join(test_dir, "NOR")
    
    os.makedirs(arc_dir, exist_ok=True)
    os.makedirs(nor_dir, exist_ok=True)
    
    # 生成模拟的信号数据（足够长以便分段处理）
    fs = 250000  # 采样频率
    duration = 0.2  # 0.2秒的数据
    t = np.linspace(0, duration, int(fs * duration))
    
    # 生成多个文件
    for i in range(3):
        # ARC信号：基础信号 + 高频故障成分 + 噪声
        base_freq = 50 + i * 10  # 基础频率变化
        fault_freq = 1000 + i * 500  # 故障频率变化
        
        arc_signal = (np.sin(2 * np.pi * base_freq * t) + 
                     0.5 * np.sin(2 * np.pi * fault_freq * t) + 
                     0.3 * np.random.randn(len(t)))
        
        # Normal信号：基础信号 + 少量噪声
        normal_signal = (np.sin(2 * np.pi * base_freq * t) + 
                        0.1 * np.random.randn(len(t)))
        
        # 保存数据
        np.savetxt(os.path.join(arc_dir, f"test_arc_{i+1}.csv"), arc_signal, delimiter=',')
        np.savetxt(os.path.join(nor_dir, f"test_normal_{i+1}.csv"), normal_signal, delimiter=',')
    
    print(f"测试数据已创建在 {test_dir} 目录下")
    print(f"每个文件包含 {len(t)} 个数据点")
    return test_dir

def test_data_pre_process():
    """
    测试data_pre_process函数
    """
    print("测试data_pre_process函数...")
    
    from fft_random_forest import data_pre_process
    
    # 创建测试数据
    test_dir = create_test_data_for_processing()
    arc_path = os.path.join(test_dir, "ARC")
    norm_path = os.path.join(test_dir, "NOR")
    
    try:
        # 参数设置
        average_width = 5
        feature_length = int(512 / average_width)
        
        # 运行data_pre_process
        feature_matrix, labels, arc_length, norm_length = data_pre_process(
            arc_path, norm_path, average_width, feature_length
        )
        
        print(f"处理成功！")
        print(f"特征矩阵形状: {feature_matrix.shape}")
        print(f"标签数组形状: {labels.shape}")
        print(f"故障样本数: {arc_length}")
        print(f"正常样本数: {norm_length}")
        print(f"特征长度: {feature_length}")
        
        # 清理测试数据
        import shutil
        shutil.rmtree(test_dir)
        
        return True, (feature_matrix, labels, arc_length, norm_length, average_width)
        
    except Exception as e:
        print(f"data_pre_process测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_processed_visualization():
    """
    测试处理后数据的可视化功能
    """
    print("测试处理后数据的可视化功能...")
    
    # 先测试data_pre_process
    success, data = test_data_pre_process()
    if not success:
        return False
    
    feature_matrix, labels, arc_length, norm_length, average_width = data
    
    try:
        from fft_visualization import visualize_processed_features
        
        # 创建输出目录
        output_dir = "test_results/processed_vis"
        
        # 运行可视化
        visualize_processed_features(
            feature_matrix, labels, arc_length, norm_length, output_dir, average_width
        )
        
        # 检查输出文件
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            print(f"生成了 {len(files)} 个可视化文件:")
            for file in files:
                print(f"  - {file}")
            return True
        else:
            print("输出目录不存在")
            return False
            
    except Exception as e:
        print(f"可视化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_function():
    """
    测试主函数（使用模拟路径）
    """
    print("测试主函数...")
    
    # 创建测试数据
    test_dir = create_test_data_for_processing()
    
    try:
        # 修改fft_visualization中的路径
        import fft_visualization
        
        # 临时修改main函数
        def test_main():
            # 测试数据路径
            test_arc_path = os.path.join(test_dir, "ARC")
            test_normal_path = os.path.join(test_dir, "NOR")
            
            # 输出目录
            output_dir = "test_results/main_processed_vis"
            
            # 参数设置
            average_width = 5
            feature_length = int(512 / average_width)
            
            print("开始使用data_pre_process方法进行FFT可视化分析...")
            print(f"输出目录: {output_dir}")
            print(f"平均窗口宽度: {average_width}")
            print(f"特征长度: {feature_length}")
            
            # 使用data_pre_process处理数据
            feature_matrix, labels, arc_length, norm_length = fft_visualization.data_pre_process(
                test_arc_path, test_normal_path, average_width, feature_length
            )
            
            print(f"\n数据处理完成:")
            print(f"特征矩阵形状: {feature_matrix.shape}")
            print(f"故障样本数: {arc_length}")
            print(f"正常样本数: {norm_length}")
            
            # 可视化处理后的特征
            print("\n开始生成可视化图表...")
            fft_visualization.visualize_processed_features(
                feature_matrix, labels, arc_length, norm_length, output_dir, average_width
            )
            
            print(f"\n可视化完成！结果保存在 {output_dir} 目录下")
        
        # 运行测试
        test_main()
        
        # 检查结果
        output_dir = "test_results/main_processed_vis"
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            print(f"主函数测试成功，生成了 {len(files)} 个文件")
            return True
        else:
            print("主函数测试失败，未生成输出文件")
            return False
            
    except Exception as e:
        print(f"主函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理测试数据
        import shutil
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)

def main_test():
    """
    主测试函数
    """
    print("=" * 60)
    print("FFT可视化功能测试 - 使用data_pre_process方法")
    print("=" * 60)
    
    tests = [
        ("data_pre_process功能", lambda: test_data_pre_process()[0]),
        ("处理后数据可视化", test_processed_visualization),
        ("主函数测试", test_main_function)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"运行测试: {test_name}")
        print(f"{'-' * 40}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            status = "通过" if result else "失败"
            print(f"测试结果: {status}")
        except Exception as e:
            print(f"测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'=' * 60}")
    print("测试总结")
    print(f"{'=' * 60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
        print("\n新的FFT可视化功能已经可以使用data_pre_process方法处理数据并生成可视化结果")
    else:
        print("⚠️  有测试失败，请检查相关功能")

if __name__ == "__main__":
    main_test()
