# FFT可视化工具

## 概述

`fft_visualization.py` 是一个用于读取AFCI数据并进行FFT变换可视化的工具。它提供两种工作模式：
1. **原始数据可视化**: 直接处理原始CSV文件，生成时域、频域和频谱图
2. **处理后数据可视化**: 复用 `fft_random_forest.py` 中的 `data_pre_process` 方法，对处理后的特征数据进行可视化

## 功能特性

- **健壮的CSV加载**: 能够处理包含引号、特殊字符的CSV文件
- **双模式FFT分析**: 支持原始数据FFT和预处理后的特征分析
- **多种可视化**: 生成时域、频域、频谱图以及特征分析图表
- **特征对比分析**: 故障与正常数据的特征分布对比
- **降维可视化**: PCA和t-SNE降维可视化
- **批量处理**: 自动处理指定目录下的所有CSV文件
- **中文支持**: 图表支持中文标题和标签

## 文件结构

```
py_utils/
├── fft_visualization.py      # 主要的FFT可视化工具（支持双模式）
├── test_fft_vis.py          # 原始数据可视化测试脚本
├── test_processed_vis.py    # 处理后数据可视化测试脚本
├── test_individual_plots.py # 个体样本图表测试脚本
├── example_individual_plots.py # 个体样本图表使用示例
├── README_FFT_VIS.md        # 本说明文件
└── fft_random_forest.py     # 随机森林分析工具（提供data_pre_process方法）
```

## 依赖要求

确保安装以下Python包：

```bash
pip install numpy matplotlib pandas scipy tqdm
```

## 使用方法

### 1. 处理后数据可视化（推荐）

使用 `data_pre_process` 方法处理数据并可视化特征：

```bash
cd py_utils
python fft_visualization.py
```

这会：
- 使用与随机森林相同的预处理方法
- 生成特征分布对比图
- 生成频率域特征分析图
- 生成PCA/t-SNE降维可视化
- 生成特征热力图和样本曲线图

结果保存到: `results/fft_vis_processed/`

### 2. 原始数据可视化

直接处理原始CSV文件：

```bash
cd py_utils
python -c "from fft_visualization import main_original; main_original()"
```

默认会处理以下路径的数据：
- ARC数据: `D:\Dev\AFCI云边协同\数据\processed\ARC`
- Normal数据: `D:\Dev\AFCI云边协同\数据\processed\NOR`

结果保存到: `results/fft_vis_original/`

### 2. 自定义路径

修改 `fft_visualization.py` 中的路径设置：

```python
# 在main()函数中修改这些路径
test_arc_path = r"你的ARC数据路径"
test_normal_path = r"你的Normal数据路径"
output_dir = "你的输出目录"
```

### 3. 生成个体样本图表

使用示例脚本：

```bash
python example_individual_plots.py
```

或作为模块使用：

```python
from fft_visualization import data_pre_process, visualize_processed_features

# 处理数据
feature_matrix, labels, arc_length, norm_length = data_pre_process(
    arc_path, normal_path, average_width, feature_length
)

# 生成可视化（包括每个样本的单独图表）
visualize_processed_features(
    feature_matrix, labels, arc_length, norm_length, output_dir,
    average_width, save_individual=True
)
```

### 4. 原始数据处理（作为模块使用）

```python
from fft_visualization import process_and_visualize_data

# 处理单个目录的数据
process_and_visualize_data(
    data_path="数据路径",
    output_dir="输出路径",
    data_type="ARC"  # 或 "NOR"
)
```

## 输出文件

### 处理后数据可视化输出

使用 `data_pre_process` 方法时，会生成以下分析图表：

1. **特征分布对比图**: `feature_distribution_comparison.png`
   - 故障与正常数据的特征均值、标准差对比
   - 特征差异分析和箱线图对比

2. **频率域特征分析**: `feature_frequency_comparison.png`
   - 基于频率的特征对比（线性和对数尺度）
   - 显示不同频率段的特征差异

3. **特征热力图**: `feature_heatmap.png`
   - ARC和Normal数据的特征热力图
   - 特征差异热力图

4. **降维可视化**: `pca_visualization.png`
   - PCA主成分分析结果
   - t-SNE降维可视化（样本数≤1000时）

5. **样本特征曲线**: `sample_feature_curves.png`
   - 随机选择的样本特征曲线
   - 故障和正常数据的特征模式对比

6. **样本汇总图表**: `sample_summary_grid.png` 和 `sample_overlay_plots.png`
   - 网格显示前20个样本的特征曲线
   - 叠加显示所有样本的分布情况

7. **个体样本图表**: `individual_samples/` 目录
   - `ARC/`: 每个故障样本的单独特征折线图
   - `NOR/`: 每个正常样本的单独特征折线图
   - 每个图表显示1×102特征矩阵的完整折线图

### 原始数据可视化输出

对每个输入的CSV文件，会生成：

1. **时域图**: `{数据类型}_{文件名}_time_domain.png`
   - 显示原始信号的时域波形

2. **频域图**: `{数据类型}_{文件名}_fft.png`
   - 包含线性幅度和对数幅度的FFT分析结果

3. **频谱图**: `{数据类型}_{文件名}_spectrogram.png`
   - 显示信号的时频分析结果（仅对长度≤100,000的数据生成）

## 主要函数说明

### `load_csv_robust(file_path)`
健壮的CSV文件加载函数，能够处理包含引号和特殊字符的数据。

**参数:**
- `file_path`: CSV文件路径

**返回:**
- `numpy.array`: 清理后的数值数组

### `perform_fft_analysis(data, fs=250000)`
对数据进行FFT分析。

**参数:**
- `data`: 输入信号数据
- `fs`: 采样频率（默认250kHz）

**返回:**
- `freq`: 频率数组
- `magnitude`: FFT幅度数组

### `process_and_visualize_data(data_path, output_dir, data_type)`
处理并可视化指定目录下的所有数据。

**参数:**
- `data_path`: 数据目录路径
- `output_dir`: 输出目录路径
- `data_type`: 数据类型标识（"ARC"或"NOR"）

## 测试

### 处理后数据可视化测试

```bash
python test_processed_vis.py
```

测试包括：
- `data_pre_process` 功能测试
- 处理后数据可视化测试
- 主函数完整流程测试

### 个体样本图表测试

```bash
python test_individual_plots.py
```

测试包括：
- 每个样本的单独图表生成
- 样本汇总图表生成
- 完整可视化流程测试

### 原始数据可视化测试

```bash
python test_fft_vis.py
```

测试包括：
- CSV加载功能测试
- FFT分析功能测试
- 完整可视化流程测试

## 配置参数

可以在代码中调整以下参数：

- **采样频率**: 默认250kHz，可在`perform_fft_analysis()`中修改
- **图像分辨率**: 默认300 DPI，可在`savefig()`调用中修改
- **频率显示范围**: 默认限制到50kHz，可在绘图函数中修改
- **处理文件数量**: 默认处理前10个文件，可在`process_and_visualize_data()`中修改

## 注意事项

1. **内存使用**: 对于大文件，频谱图生成可能消耗较多内存，因此限制了数据长度
2. **文件格式**: 目前支持CSV格式，数据应为数值型
3. **中文字体**: 如果中文显示有问题，请确保系统安装了SimHei或Microsoft YaHei字体
4. **路径格式**: Windows路径建议使用原始字符串（r"路径"）

## 故障排除

### 常见问题

1. **ImportError: No module named 'matplotlib'**
   ```bash
   pip install matplotlib
   ```

2. **中文显示为方块**
   - 检查系统是否安装中文字体
   - 或修改代码中的字体设置

3. **文件加载失败**
   - 检查CSV文件格式
   - 确认文件路径正确
   - 查看错误信息中的具体原因

4. **内存不足**
   - 减少同时处理的文件数量
   - 对大文件进行分段处理

## 扩展功能

可以根据需要添加以下功能：

- 支持更多文件格式（如TXT、MAT等）
- 添加滤波功能
- 实现交互式可视化
- 添加统计分析功能
- 支持批量参数配置

## 版本历史

- v1.0: 初始版本，支持基本的FFT可视化功能
- 包含健壮的CSV加载和多种图表生成功能
