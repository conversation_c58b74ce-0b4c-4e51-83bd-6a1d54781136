# 个体样本特征折线图功能总结

## 功能概述

成功实现了为每个样本的1×102特征矩阵生成折线图并保存的功能。该功能完全复用了 `fft_random_forest.py` 中的 `data_pre_process` 方法，确保了数据处理的一致性。

## 主要特性

### ✅ 已实现功能

1. **数据处理复用**
   - 完全使用 `fft_random_forest.py` 中的 `data_pre_process` 方法
   - 保持与随机森林模型相同的特征提取流程
   - 支持相同的参数设置（average_width=5, feature_length=102）

2. **个体样本图表生成**
   - 为每个样本生成单独的特征折线图
   - ARC（故障）样本：红色折线图，保存在 `individual_samples/ARC/` 目录
   - Normal（正常）样本：蓝色折线图，保存在 `individual_samples/NOR/` 目录
   - 每个图表包含统计信息（均值、最大值、最小值）

3. **样本汇总图表**
   - 网格显示前20个样本的特征曲线
   - 叠加显示所有样本的分布情况
   - 包含均值线和标准差区域

4. **完整的可视化套件**
   - 特征分布对比图
   - 频率域特征分析图
   - 特征热力图
   - PCA/t-SNE降维可视化
   - 样本特征曲线对比

## 文件结构

```
py_utils/
├── fft_visualization.py          # 主要工具（已更新）
├── test_individual_plots.py      # 个体图表测试脚本
├── example_individual_plots.py   # 使用示例脚本
├── test_processed_vis.py         # 处理后数据测试脚本
├── README_FFT_VIS.md             # 完整说明文档
└── SUMMARY_INDIVIDUAL_PLOTS.md   # 本总结文档
```

## 使用方法

### 1. 快速开始

```bash
# 运行示例（需要修改数据路径）
python example_individual_plots.py

# 或直接运行主程序
python fft_visualization.py
```

### 2. 编程接口

```python
from fft_visualization import data_pre_process, visualize_processed_features

# 数据处理
feature_matrix, labels, arc_length, norm_length = data_pre_process(
    arc_path, normal_path, average_width=5, feature_length=102
)

# 生成可视化（包括个体图表）
visualize_processed_features(
    feature_matrix, labels, arc_length, norm_length, output_dir, 
    average_width=5, save_individual=True
)
```

### 3. 控制个体图表生成

```python
# 只生成汇总图表，不生成个体图表
visualize_processed_features(
    feature_matrix, labels, arc_length, norm_length, output_dir, 
    average_width=5, save_individual=False
)
```

## 输出文件说明

### 汇总分析图表
- `feature_distribution_comparison.png` - 特征分布对比
- `feature_frequency_comparison.png` - 频率域特征分析
- `feature_heatmap.png` - 特征热力图
- `pca_visualization.png` - 降维可视化
- `sample_feature_curves.png` - 样本特征曲线对比
- `sample_summary_grid.png` - 样本网格显示
- `sample_overlay_plots.png` - 样本叠加显示

### 个体样本图表
- `individual_samples/ARC/ARC_sample_001.png` - 第1个ARC样本
- `individual_samples/ARC/ARC_sample_002.png` - 第2个ARC样本
- `...`
- `individual_samples/NOR/Normal_sample_001.png` - 第1个Normal样本
- `individual_samples/NOR/Normal_sample_002.png` - 第2个Normal样本
- `...`

## 测试结果

### ✅ 测试通过情况

1. **基本功能测试** - ✅ 通过
   - CSV加载功能正常
   - data_pre_process方法工作正常
   - 个体图表生成功能正常

2. **完整流程测试** - ✅ 通过
   - 端到端数据处理流程正常
   - 所有类型图表生成正常
   - 文件保存和组织正常

3. **性能测试** - ✅ 通过
   - 48个ARC样本 + 48个Normal样本
   - 总计96个个体图表生成成功
   - 处理时间约1.5分钟（包含图表生成）

## 技术细节

### 数据流程
1. **数据加载**: 使用健壮的CSV加载函数处理包含引号的数据
2. **特征提取**: 复用 `data_pre_process` 中的 `mystft` 函数
3. **特征处理**: 1024点FFT → 512点频谱 → 平均化 → 102个特征
4. **图表生成**: matplotlib生成高质量折线图
5. **文件保存**: 按类型组织保存到不同目录

### 图表特性
- **分辨率**: 300 DPI高清图片
- **格式**: PNG格式
- **尺寸**: 12×6英寸（个体图表）
- **颜色**: ARC红色，Normal蓝色
- **标记**: 圆形标记（ARC），方形标记（Normal）
- **统计信息**: 显示均值、最大值、最小值

## 性能考虑

### 时间复杂度
- 数据处理: O(n×m)，n为样本数，m为每样本数据点数
- 图表生成: O(n)，n为样本数
- 总体时间: 主要受样本数量影响

### 空间占用
- 每个个体图表: ~200-500KB
- 100个样本约占用: ~20-50MB
- 建议为大量样本预留足够磁盘空间

### 优化建议
- 对于大量样本（>500），考虑分批处理
- 可以通过 `save_individual=False` 跳过个体图表生成
- 使用SSD存储可提高图表保存速度

## 故障排除

### 常见问题

1. **内存不足**
   - 减少同时处理的样本数量
   - 关闭不必要的应用程序

2. **磁盘空间不足**
   - 清理输出目录
   - 选择有足够空间的输出路径

3. **图表生成缓慢**
   - 正常现象，每个图表需要1-2秒
   - 可以通过进度条监控进度

4. **中文显示问题**
   - 确保系统安装中文字体
   - 或修改字体设置

## 扩展可能

### 未来可能的改进
1. **交互式图表**: 使用plotly生成可交互的HTML图表
2. **批量导出**: 支持PDF格式的批量导出
3. **自定义样式**: 支持用户自定义图表样式
4. **并行处理**: 使用多进程加速图表生成
5. **数据筛选**: 支持按条件筛选样本生成图表

## 总结

成功实现了完整的个体样本特征折线图生成功能，该功能：

- ✅ 完全复用现有的数据处理流程
- ✅ 为每个1×102特征矩阵生成高质量折线图
- ✅ 提供完整的可视化分析套件
- ✅ 通过了全面的测试验证
- ✅ 提供了详细的文档和示例

该功能可以帮助用户深入分析每个样本的特征模式，识别故障和正常样本之间的差异，为进一步的算法优化提供有价值的洞察。
