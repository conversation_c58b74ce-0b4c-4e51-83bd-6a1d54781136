clc;
clear;
close all;
load('remove24_tree15_minleaf200');
numTrees = 15; % 决策树的数量
numNodes = 1627; %每棵树的节点数量

% 导出森林模型到 C 语言代码
exportForestToC(net, numTrees)

function exportForestToC(forest, numTrees)
    % 打开文件以写入
    fileID = fopen('forest.c', 'w');
    
    % 包含必要的头文件
    fprintf(fileID, '#include <stdio.h>\n');
    fprintf(fileID, '#include <string.h>\n');
    fprintf(fileID, '#include <stdint.h>\n');
    fprintf(fileID, 'float predictTree(float features[], float cutPoint[], uint8_t cutPredictor[], uint16_t classProbability[], short children[][2], int nodeIndex);\n');

    % 定义结构体数据
    % fprintf(fileID, 'typedef struct TreeNode { \n');
    % fprintf(fileID, '   float cutPoint[%d]; \n',numNodes);
    % fprintf(fileID, '   uint8_t cutPredictor[%d]; \n',numNodes);
    % fprintf(fileID, '   float children[%d][2]; \n',numNodes);
    % fprintf(fileID, '   float classProbability[%d]; \n',numNodes);
    % fprintf(fileID, '}TreeNode; \n');


    % 为每棵树生成 C 语言函数声明
    % for t = 1:numTrees
    %     fprintf(fileID, 'int predictTree%d(double features[]);\n', t);
    % end



    % 生成每棵树的参数数据
    for t = 1:numTrees
        tree = forest.Trees{t};
        %初始化cutPoint
        nodes = tree.CutPoint; % 划分阈值，若为类别则为NaN
        cutVar = tree.CutPredictor; % 用哪个特征值来划分，若没有则为空
        ClassProbability = tree.ClassProbability; % 获取节点分类
        children = tree.Children; % 获取子节点
        %将树数据存入数组
        strCutPoint = '{';
        strCutPredictor = '{';
        strClassProbability = '{';
        strChildren = '{';
        for i=1:length(nodes)
            if i > 1
                strCutPoint = strcat(strCutPoint, ', ');
                strCutPredictor = strcat(strCutPredictor, ', ');
                strClassProbability = strcat(strClassProbability, ', ');
                strChildren = strcat(strChildren, ', ');
            end
    
            % 将数据转换为字符串并拼接到结果字符串中
            if isnan(nodes(i))
                strCutPoint = strcat(strCutPoint, '100'); 
            else
                strCutPoint = strcat(strCutPoint, num2str(nodes(i))); 
            end
            
            % 获取特征名称
            predictorName = cutVar{i};
            % 把特征名称转为数字
            numStr = regexp(predictorName, '\d+', 'match'); % 提取数字部分
    
            if length(numStr) > 0
                strCutPredictor = strcat(strCutPredictor, numStr{1});
            else
                strCutPredictor = strcat(strCutPredictor, '0');
            end
    
            tmp = round(ClassProbability(i)*10000);
            strClassProbability = strcat(strClassProbability, num2str(tmp));
    
            strChildren = strcat(strChildren,'{');
            strChildren = strcat(strChildren,num2str(children(i,1)));
            strChildren = strcat(strChildren,',');
            strChildren = strcat(strChildren,num2str(children(i,2)));
            strChildren = strcat(strChildren,'}');
            
            
    
        end
        fprintf(fileID, 'const float cutPoint%d[] = %s };  \n',t , strCutPoint);
        fprintf(fileID, 'const uint8_t cutPredictor%d[] = %s }; \n',t , strCutPredictor);
        fprintf(fileID, 'const uint16_t classProbability%d[] = %s }; \n',t , strClassProbability);
        fprintf(fileID, 'const short children%d[][2] = %s }; \n',t , strChildren);
        fprintf(fileID, '\n');

        generateTreeCode(fileID, tree, t);
    end

        % 主预测函数
    fprintf(fileID, '\n int predictForest(double features[]) {\n');
    fprintf(fileID, '    float resultClass = 0;\n'); % 结果概率
    for t = 1:numTrees
        fprintf(fileID, '    float class%d = predictTree(features, cutPoint%d, cutPredictor%d, classProbability%d, children%d, 0);\n', t,t,t,t,t);
        fprintf(fileID, '    resultClass = resultClass + (class%d - resultClass)/%d.0;\n', t, t);
    end
    fprintf(fileID, '    if(resultClass > 0.5){ \n');
    fprintf(fileID, '        return 0; \n');
    fprintf(fileID, '    }else{ \n');
    fprintf(fileID, '        return 1; }} \n');

    % 进行预测的逻辑
    fprintf(fileID, '\n float predictTree(float features[], float cutPoint[], uint8_t cutPredictor[], uint16_t classProbability[], short children[][2], int nodeIndex){ \n');
    fprintf(fileID, '   if(cutPoint[nodeIndex] == 100){ \n');
    fprintf(fileID, '       return classProbability[nodeIndex] / 10000.0;\n');
    fprintf(fileID, '   } \n');
    fprintf(fileID, '   else{ \n');
    fprintf(fileID, '       if(features[cutPredictor[nodeIndex] - 1] < cutPoint[nodeIndex]){ \n');
    fprintf(fileID, '           predictTree(features, cutPoint, cutPredictor, classProbability, children, children[nodeIndex][0] -1);\n');
    fprintf(fileID, '        }else{\n');
    fprintf(fileID, '           predictTree(features, cutPoint, cutPredictor, classProbability, children, children[nodeIndex][1] -1);\n');
    fprintf(fileID, '  }}} \n');

    % 关闭文件
    fclose(fileID);
end


function generateTreeCode(fileID, tree, treeIndex)
    % fprintf(fileID, 'int predictTree%d(double features[]) {\n', treeIndex);
    % fprintf(fileID, '    TreeNode* node%d = (TreeNode*)malloc(sizeof(TreeNode)); \n', treeIndex);

    % 生成树的节点代码
    % generateNodeCode(fileID, nodes, cutVar, children, classProbability, 1);
    % fprintf(fileID, 'return nodePredictor(features, cutPoint%d, cutPredictor%d, classProbability%d, children%d,0 ); \n',treeIndex);
 
    % fprintf(fileID, '}\n\n');
end

function generateNodeCode(fileID, nodes, cutVar, children, classProbability, nodeIndex)
    if isnan(nodes(nodeIndex)) % 叶节点的 CutPoint 通常为 NaN
        % 生成叶节点的返回分类代码
        fprintf(fileID, '    return %d;\n', cellfun(@str2double,classProbability(nodeIndex)));
    else
        % 获取特征名称
        predictorName = cutVar{nodeIndex};
        % 把特征名称转为数字
        numStr = regexp(predictorName, '\d+', 'match'); % 提取数字部分
        predictorName = str2double(numStr{1}); % 转换为数值       
        
        % 生成分支节点的代码
        fprintf(fileID, '    if (features[%d] < %.6f) {\n', predictorName-1, nodes(nodeIndex));
        generateNodeCode(fileID, nodes, cutVar, children, classProbability, children(nodeIndex, 1));
        fprintf(fileID, '    } else {\n');
        generateNodeCode(fileID, nodes, cutVar, children, classProbability, children(nodeIndex, 2));
        fprintf(fileID, '    }\n');
    end
end



