"""
使用示例：为每个样本的1×102特征矩阵生成折线图

这个脚本展示如何使用修改后的FFT可视化工具来：
1. 使用data_pre_process方法处理数据
2. 为每个样本生成单独的特征折线图
3. 生成汇总分析图表
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """
    主函数：演示如何使用新的个体样本绘图功能
    """
    print("=" * 60)
    print("个体样本特征折线图生成示例")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from fft_visualization import data_pre_process, visualize_processed_features
        
        # 数据路径（请根据实际情况修改）
        arc_path = r"D:\Dev\AFCI云边协同\数据\processed\ARC"
        normal_path = r"D:\Dev\AFCI云边协同\数据\processed\NOR"
        
        # 输出目录
        output_dir = "results/individual_sample_plots"
        
        # 参数设置（与fft_random_forest.py保持一致）
        average_width = 5
        feature_length = int(512 / average_width)  # 102个特征
        
        print(f"数据路径:")
        print(f"  ARC数据: {arc_path}")
        print(f"  Normal数据: {normal_path}")
        print(f"输出目录: {output_dir}")
        print(f"特征参数:")
        print(f"  平均窗口宽度: {average_width}")
        print(f"  特征长度: {feature_length}")
        
        # 检查数据路径是否存在
        if not os.path.exists(arc_path):
            print(f"\n❌ ARC数据路径不存在: {arc_path}")
            print("请修改脚本中的路径设置")
            return False
            
        if not os.path.exists(normal_path):
            print(f"\n❌ Normal数据路径不存在: {normal_path}")
            print("请修改脚本中的路径设置")
            return False
        
        print(f"\n开始数据处理...")
        
        # 步骤1: 使用data_pre_process处理数据
        feature_matrix, labels, arc_length, norm_length = data_pre_process(
            arc_path, normal_path, average_width, feature_length
        )
        
        print(f"\n数据处理完成:")
        print(f"  特征矩阵形状: {feature_matrix.shape}")
        print(f"  每个样本特征数: {feature_matrix.shape[1]} (1×{feature_matrix.shape[1]})")
        print(f"  故障样本数: {arc_length}")
        print(f"  正常样本数: {norm_length}")
        print(f"  总样本数: {len(labels)}")
        
        # 步骤2: 生成可视化图表（包括每个样本的单独图表）
        print(f"\n开始生成可视化图表...")
        print(f"这将包括:")
        print(f"  - 汇总分析图表")
        print(f"  - 每个样本的单独特征折线图 ({arc_length + norm_length} 个)")
        
        # 询问用户是否继续（因为生成大量图表可能需要时间）
        if arc_length + norm_length > 100:
            response = input(f"\n将生成 {arc_length + norm_length} 个单独的样本图表，这可能需要较长时间。继续吗？(y/n): ")
            if response.lower() != 'y':
                print("已取消操作")
                return False
        
        # 生成可视化图表
        visualize_processed_features(
            feature_matrix, labels, arc_length, norm_length, output_dir, 
            average_width, save_individual=True
        )
        
        print(f"\n✅ 可视化完成！")
        
        # 检查输出结果
        print(f"\n生成的文件:")
        
        # 汇总图表
        summary_files = [f for f in os.listdir(output_dir) if f.endswith('.png')]
        print(f"\n汇总分析图表 ({len(summary_files)} 个):")
        for file in summary_files:
            print(f"  ✓ {file}")
        
        # 个体样本图表
        arc_dir = os.path.join(output_dir, "individual_samples", "ARC")
        norm_dir = os.path.join(output_dir, "individual_samples", "NOR")
        
        if os.path.exists(arc_dir):
            arc_files = os.listdir(arc_dir)
            print(f"\nARC样本个体图表 ({len(arc_files)} 个):")
            print(f"  保存在: {arc_dir}")
            for file in arc_files[:3]:  # 只显示前3个
                print(f"  ✓ {file}")
            if len(arc_files) > 3:
                print(f"  ... 还有 {len(arc_files) - 3} 个文件")
        
        if os.path.exists(norm_dir):
            norm_files = os.listdir(norm_dir)
            print(f"\nNormal样本个体图表 ({len(norm_files)} 个):")
            print(f"  保存在: {norm_dir}")
            for file in norm_files[:3]:  # 只显示前3个
                print(f"  ✓ {file}")
            if len(norm_files) > 3:
                print(f"  ... 还有 {len(norm_files) - 3} 个文件")
        
        print(f"\n📊 图表说明:")
        print(f"每个样本的特征折线图显示:")
        print(f"  - X轴: 特征索引 (0-{feature_length-1})")
        print(f"  - Y轴: 特征值")
        print(f"  - 红色线条: ARC (故障) 样本")
        print(f"  - 蓝色线条: Normal (正常) 样本")
        print(f"  - 图表包含统计信息 (均值、最大值、最小值)")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_usage():
    """
    显示使用说明
    """
    print("=" * 60)
    print("个体样本特征折线图生成工具使用说明")
    print("=" * 60)
    print()
    print("功能:")
    print("  - 使用data_pre_process方法处理AFCI数据")
    print("  - 为每个样本的1×102特征矩阵生成折线图")
    print("  - 生成汇总分析图表")
    print()
    print("使用方法:")
    print("  1. 修改脚本中的数据路径:")
    print("     arc_path = r'你的ARC数据路径'")
    print("     normal_path = r'你的Normal数据路径'")
    print()
    print("  2. 运行脚本:")
    print("     python example_individual_plots.py")
    print()
    print("输出文件:")
    print("  - results/individual_sample_plots/")
    print("    ├── 汇总分析图表 (*.png)")
    print("    └── individual_samples/")
    print("        ├── ARC/ (故障样本图表)")
    print("        └── NOR/ (正常样本图表)")
    print()
    print("注意事项:")
    print("  - 确保数据路径正确")
    print("  - 大量样本可能需要较长处理时间")
    print("  - 每个样本图表约占用几百KB空间")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
        show_usage()
    else:
        success = main()
        if success:
            print("\n🎉 示例运行成功！")
        else:
            print("\n❌ 示例运行失败")
            print("\n使用 'python example_individual_plots.py --help' 查看使用说明")
