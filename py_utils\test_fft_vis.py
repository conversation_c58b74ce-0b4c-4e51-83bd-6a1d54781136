"""
测试FFT可视化功能的脚本
"""
import os
import sys
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fft_visualization import main, load_csv_robust, perform_fft_analysis

def create_test_data():
    """
    创建测试数据文件
    """
    # 创建测试目录
    test_dir = "test_data"
    arc_dir = os.path.join(test_dir, "ARC")
    nor_dir = os.path.join(test_dir, "NOR")
    
    os.makedirs(arc_dir, exist_ok=True)
    os.makedirs(nor_dir, exist_ok=True)
    
    # 生成模拟的故障信号（包含高频噪声）
    fs = 250000  # 采样频率
    t = np.linspace(0, 0.1, int(fs * 0.1))  # 0.1秒的数据
    
    # ARC信号：基础信号 + 高频故障成分
    arc_signal = np.sin(2 * np.pi * 50 * t) + 0.5 * np.sin(2 * np.pi * 1000 * t) + 0.3 * np.random.randn(len(t))
    
    # Normal信号：基础信号 + 少量噪声
    normal_signal = np.sin(2 * np.pi * 50 * t) + 0.1 * np.random.randn(len(t))
    
    # 保存测试数据
    np.savetxt(os.path.join(arc_dir, "test_arc_1.csv"), arc_signal, delimiter=',')
    np.savetxt(os.path.join(arc_dir, "test_arc_2.csv"), arc_signal * 1.2, delimiter=',')
    np.savetxt(os.path.join(nor_dir, "test_normal_1.csv"), normal_signal, delimiter=',')
    np.savetxt(os.path.join(nor_dir, "test_normal_2.csv"), normal_signal * 0.8, delimiter=',')
    
    print(f"测试数据已创建在 {test_dir} 目录下")
    return test_dir

def test_load_csv_function():
    """
    测试CSV加载函数
    """
    print("测试CSV加载函数...")
    
    # 创建包含引号的测试CSV文件
    test_file = "test_with_quotes.csv"
    with open(test_file, 'w') as f:
        f.write('"1.5","2.3","3.7"\n')
        f.write('4.2,"5.8",6.1\n')
        f.write('"7.9",8.4,"9.2"\n')
    
    try:
        data = load_csv_robust(test_file)
        print(f"成功加载数据，长度: {len(data)}")
        print(f"数据前5个值: {data[:5]}")
        
        # 清理测试文件
        os.remove(test_file)
        return True
    except Exception as e:
        print(f"CSV加载测试失败: {e}")
        return False

def test_fft_analysis():
    """
    测试FFT分析功能
    """
    print("测试FFT分析功能...")
    
    # 创建测试信号
    fs = 1000
    t = np.linspace(0, 1, fs)
    signal = np.sin(2 * np.pi * 50 * t) + 0.5 * np.sin(2 * np.pi * 120 * t)
    
    try:
        freq, magnitude = perform_fft_analysis(signal, fs)
        print(f"FFT分析成功，频率范围: {freq[0]:.2f} - {freq[-1]:.2f} Hz")
        print(f"幅度范围: {magnitude.min():.2f} - {magnitude.max():.2f}")
        return True
    except Exception as e:
        print(f"FFT分析测试失败: {e}")
        return False

def run_test_visualization():
    """
    运行可视化测试
    """
    print("开始可视化测试...")
    
    # 创建测试数据
    test_dir = create_test_data()
    
    # 修改fft_visualization.py中的路径为测试路径
    import fft_visualization
    
    # 临时修改路径
    original_main = fft_visualization.main
    
    def test_main():
        # 测试数据路径
        test_arc_path = os.path.join(test_dir, "ARC")
        test_normal_path = os.path.join(test_dir, "NOR")
        
        # 输出目录
        output_dir = "test_results/fft_vis"
        
        print("开始FFT可视化分析...")
        print(f"输出目录: {output_dir}")
        
        # 处理ARC数据
        if os.path.exists(test_arc_path):
            print("\n处理ARC（故障）数据...")
            fft_visualization.process_and_visualize_data(test_arc_path, output_dir, "ARC")
        else:
            print(f"ARC数据路径不存在: {test_arc_path}")
        
        # 处理Normal数据
        if os.path.exists(test_normal_path):
            print("\n处理Normal（正常）数据...")
            fft_visualization.process_and_visualize_data(test_normal_path, output_dir, "NOR")
        else:
            print(f"Normal数据路径不存在: {test_normal_path}")
        
        print(f"\n可视化完成！结果保存在 {output_dir} 目录下")
    
    # 运行测试
    try:
        test_main()
        print("可视化测试成功完成！")
        
        # 检查输出文件
        output_dir = "test_results/fft_vis"
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            print(f"生成了 {len(files)} 个可视化文件:")
            for file in files[:5]:  # 只显示前5个文件
                print(f"  - {file}")
            if len(files) > 5:
                print(f"  ... 还有 {len(files) - 5} 个文件")
        
        return True
    except Exception as e:
        print(f"可视化测试失败: {e}")
        return False
    finally:
        # 清理测试数据
        import shutil
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)

def main_test():
    """
    主测试函数
    """
    print("=" * 50)
    print("FFT可视化功能测试")
    print("=" * 50)
    
    tests = [
        ("CSV加载功能", test_load_csv_function),
        ("FFT分析功能", test_fft_analysis),
        ("可视化功能", run_test_visualization)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'-' * 30}")
        print(f"运行测试: {test_name}")
        print(f"{'-' * 30}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            status = "通过" if result else "失败"
            print(f"测试结果: {status}")
        except Exception as e:
            print(f"测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'=' * 50}")
    print("测试总结")
    print(f"{'=' * 50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
    else:
        print("⚠️  有测试失败，请检查相关功能")

if __name__ == "__main__":
    main_test()
