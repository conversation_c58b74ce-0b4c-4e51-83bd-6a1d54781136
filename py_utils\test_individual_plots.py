"""
测试每个样本的特征折线图生成功能
"""
import os
import sys
import numpy as np
import time

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_small_test_data():
    """
    创建小规模测试数据（减少样本数量以便快速测试）
    """
    print("创建小规模测试数据...")
    
    # 创建测试目录
    test_dir = "test_individual_plots"
    arc_dir = os.path.join(test_dir, "ARC")
    nor_dir = os.path.join(test_dir, "NOR")
    
    os.makedirs(arc_dir, exist_ok=True)
    os.makedirs(nor_dir, exist_ok=True)
    
    # 生成较少的数据以便快速测试
    fs = 250000  # 采样频率
    duration = 0.1  # 0.1秒的数据
    t = np.linspace(0, duration, int(fs * duration))
    
    # 只生成2个文件
    for i in range(2):
        # ARC信号：基础信号 + 高频故障成分 + 噪声
        base_freq = 50 + i * 10
        fault_freq = 1000 + i * 500
        
        arc_signal = (np.sin(2 * np.pi * base_freq * t) + 
                     0.5 * np.sin(2 * np.pi * fault_freq * t) + 
                     0.3 * np.random.randn(len(t)))
        
        # Normal信号：基础信号 + 少量噪声
        normal_signal = (np.sin(2 * np.pi * base_freq * t) + 
                        0.1 * np.random.randn(len(t)))
        
        # 保存数据
        np.savetxt(os.path.join(arc_dir, f"test_arc_{i+1}.csv"), arc_signal, delimiter=',')
        np.savetxt(os.path.join(nor_dir, f"test_normal_{i+1}.csv"), normal_signal, delimiter=',')
    
    print(f"测试数据已创建在 {test_dir} 目录下")
    print(f"每个文件包含 {len(t)} 个数据点")
    return test_dir

def test_individual_plots():
    """
    测试个体样本绘图功能
    """
    print("=" * 60)
    print("测试每个样本的特征折线图生成功能")
    print("=" * 60)
    
    # 创建测试数据
    test_dir = create_small_test_data()
    
    try:
        from fft_visualization import data_pre_process, save_individual_sample_plots, create_sample_summary_plots
        
        # 设置路径
        arc_path = os.path.join(test_dir, "ARC")
        norm_path = os.path.join(test_dir, "NOR")
        output_dir = "test_results/individual_plots"
        
        # 参数设置
        average_width = 5
        feature_length = int(512 / average_width)
        
        print(f"\n开始数据处理...")
        print(f"平均窗口宽度: {average_width}")
        print(f"特征长度: {feature_length}")
        
        start_time = time.time()
        
        # 使用data_pre_process处理数据
        feature_matrix, labels, arc_length, norm_length = data_pre_process(
            arc_path, norm_path, average_width, feature_length
        )
        
        processing_time = time.time() - start_time
        
        print(f"\n数据处理完成 (耗时: {processing_time:.2f}秒):")
        print(f"  特征矩阵形状: {feature_matrix.shape}")
        print(f"  故障样本数: {arc_length}")
        print(f"  正常样本数: {norm_length}")
        print(f"  每个样本特征数: {feature_matrix.shape[1]}")
        
        # 显示一些样本特征
        print(f"\n样本特征预览:")
        print(f"  第1个ARC样本特征前5个值: {feature_matrix[0][:5]}")
        print(f"  第1个Normal样本特征前5个值: {feature_matrix[arc_length][:5]}")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 测试1: 生成样本汇总图表
        print(f"\n测试1: 生成样本汇总图表...")
        summary_start = time.time()
        create_sample_summary_plots(feature_matrix, labels, arc_length, norm_length, output_dir)
        summary_time = time.time() - summary_start
        print(f"汇总图表生成完成 (耗时: {summary_time:.2f}秒)")
        
        # 测试2: 生成每个样本的单独图表
        print(f"\n测试2: 生成每个样本的单独图表...")
        individual_start = time.time()
        save_individual_sample_plots(feature_matrix, labels, arc_length, norm_length, output_dir)
        individual_time = time.time() - individual_start
        print(f"单独图表生成完成 (耗时: {individual_time:.2f}秒)")
        
        # 检查输出文件
        print(f"\n检查输出文件...")
        
        # 检查汇总图表
        summary_files = [f for f in os.listdir(output_dir) if f.endswith('.png')]
        print(f"汇总图表文件 ({len(summary_files)} 个):")
        for file in summary_files:
            print(f"  ✓ {file}")
        
        # 检查个体样本图表
        arc_individual_dir = os.path.join(output_dir, "individual_samples", "ARC")
        norm_individual_dir = os.path.join(output_dir, "individual_samples", "NOR")
        
        if os.path.exists(arc_individual_dir):
            arc_files = os.listdir(arc_individual_dir)
            print(f"\nARC个体样本图表 ({len(arc_files)} 个):")
            for file in arc_files[:5]:  # 只显示前5个
                print(f"  ✓ {file}")
            if len(arc_files) > 5:
                print(f"  ... 还有 {len(arc_files) - 5} 个文件")
        
        if os.path.exists(norm_individual_dir):
            norm_files = os.listdir(norm_individual_dir)
            print(f"\nNormal个体样本图表 ({len(norm_files)} 个):")
            for file in norm_files[:5]:  # 只显示前5个
                print(f"  ✓ {file}")
            if len(norm_files) > 5:
                print(f"  ... 还有 {len(norm_files) - 5} 个文件")
        
        total_time = time.time() - start_time
        print(f"\n测试完成！")
        print(f"总耗时: {total_time:.2f}秒")
        print(f"结果保存在: {output_dir}")
        
        # 提供查看建议
        print(f"\n生成的图表类型:")
        print(f"  1. sample_summary_grid.png - 网格显示前20个样本")
        print(f"  2. sample_overlay_plots.png - 叠加显示所有样本")
        print(f"  3. individual_samples/ARC/ - 每个ARC样本的单独图表")
        print(f"  4. individual_samples/NOR/ - 每个Normal样本的单独图表")
        
        return True
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试数据
        print(f"\n清理测试数据...")
        import shutil
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            print(f"已删除临时目录: {test_dir}")

def test_with_limited_samples():
    """
    测试限制样本数量的情况
    """
    print("\n" + "=" * 60)
    print("测试限制样本数量的个体图表生成")
    print("=" * 60)
    
    # 创建测试数据
    test_dir = create_small_test_data()
    
    try:
        from fft_visualization import data_pre_process, visualize_processed_features
        
        # 设置路径
        arc_path = os.path.join(test_dir, "ARC")
        norm_path = os.path.join(test_dir, "NOR")
        output_dir = "test_results/limited_individual_plots"
        
        # 参数设置
        average_width = 5
        feature_length = int(512 / average_width)
        
        print(f"使用完整的visualize_processed_features函数...")
        
        # 使用data_pre_process处理数据
        feature_matrix, labels, arc_length, norm_length = data_pre_process(
            arc_path, norm_path, average_width, feature_length
        )
        
        print(f"数据处理完成，开始可视化...")
        
        # 使用完整的可视化功能（包括个体图表）
        visualize_processed_features(
            feature_matrix, labels, arc_length, norm_length, output_dir, 
            average_width, save_individual=True
        )
        
        print(f"完整可视化测试成功！")
        return True
        
    except Exception as e:
        print(f"完整可视化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试数据
        import shutil
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)

def main():
    """
    主测试函数
    """
    print("开始测试个体样本图表生成功能...")
    
    # 测试1: 基本功能测试
    success1 = test_individual_plots()
    
    # 测试2: 完整流程测试
    success2 = test_with_limited_samples()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if success1:
        print("✓ 基本个体图表功能测试通过")
    else:
        print("✗ 基本个体图表功能测试失败")
    
    if success2:
        print("✓ 完整流程测试通过")
    else:
        print("✗ 完整流程测试失败")
    
    if success1 and success2:
        print("\n🎉 所有测试都通过了！")
        print("每个样本的1×102特征矩阵都可以成功绘制成折线图并保存")
    else:
        print("\n❌ 有测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
