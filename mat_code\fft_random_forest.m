%****************************************************************************%
%******************************数据分割**************************************%
%*************将原始电流数据分割成以1024个电流数据为单位的时域样本************%
%****************************************************************************%
% 清空变量与命令行
clear;
clc;close all

% 显示当前日期和时间
currentDateTime = datetime('now');
fprintf('%s ', currentDateTime);
disp('开始执行')



定义输入待分割的电流数据路径
% %注意：这里需要将故障和正常的电流数据分别放在不同的文件夹路径下
path_arc = 'D:\AFCI\原始数据-5&24\训练数据（ARC_Normal）\ARC';        %存放故障电流数据的文件夹地址
path_normal = 'D:\AFCI\原始数据-5&24\训练数据（ARC_Normal）\Normal';  %存放正常电流数据的文件夹地址


设置随机数种子
rng("default");

创建标签
average_width = 5
feature_length = fix(512/average_width)
[matrix_data2,label,arc_length,norm_length] = dataPreProcess(path_arc,path_normal,average_width,feature_length)
% %***********************************************************************************%
% %********************************随机森林模型训练***********************************%
% %***********************************************************************************%
分割训练集和验证集
train_ratio = 1;
arc_length_train = fix(train_ratio*arc_length);
arc_length_test = arc_length - arc_length_train;

norm_length_train = fix(train_ratio*norm_length);
norm_length_test = norm_length - norm_length_train;

train_length = arc_length_train + norm_length_train;
test_length = arc_length_test + norm_length_test;

% 随机产生训练集和测试集
n = randperm(size(matrix_data2,1));
% 训练集——arc_length_train个样本
train_matrix = matrix_data2(n(1:train_length),:);
train_label = label(n(1:train_length),:);
% 测试集——test_length个样本
test_matrix = matrix_data2(n((train_length+1):end),:);
test_label = label(n((train_length+1):end),:);

数据归一化，模型训练
[Train_matrix,PS] = mapminmax(train_matrix');
Train_matrix = Train_matrix';
Test_matrix = mapminmax('apply',test_matrix',PS);
Test_matrix = Test_matrix';


trees = 15;                                     
leaf  = 200;                                     
OOBPrediction = 'off';                            
OOBPredictorImportance = 'on'; 
Method = 'classification';            
paroptions = statset('UseParallel',true);
net = TreeBagger(trees, train_matrix, train_label, 'OOBPredictorImportance', OOBPredictorImportance, ...
      'Method', Method, 'OOBPrediction', OOBPrediction, 'minleaf', leaf,'MergeLeaves','off','InBagFraction',1,'Options',paroptions);
importance = net.OOBPermutedPredictorDeltaError;  


%显示当前日期和时间
currentDateTime = datetime('now');
fprintf('%s ', currentDateTime);
disp('模型训练已完成')
save('remove24_tree15_minleaf200.mat','net');

%***********************************************************************************************************%
%********************************************测试验证*******************************************************%
%***********************************************************************************************************%


定义测试数据路径
stable_path = '';
path_arc_test = ['D:\AFCI\part_data\ARC', stable_path];      %arc数据文件夹地址
path_norm_test = ['D:\AFCI\part_data\Normal', stable_path];  %normal数据文件夹地址
[test_matrix_data,label_test,arc_test_length,normal_test_length] = dataPreProcess(path_arc_test,path_norm_test,average_width,feature_length);
total_test_length = arc_test_length + normal_test_length


仿真测试
t_sim1 = predict(net, train_matrix);
% view(net.Trees{2},Mode="graph")
t_sim2 = predict(net, test_matrix_data);

格式转换
T_sim1 = str2num(cell2mat(t_sim1)); T_sim2 = str2num(cell2mat(t_sim2)); T_sim1 = cellfun(@str2num, t_sim1);
T_sim2 = cellfun(@str2num, t_sim2);

 相关指标计算
R1 = 1 - norm(train_label - T_sim1)^2 / norm(train_label - mean(train_label))^2;
R2 = 1 - norm(label_test - T_sim2)^2 / norm(label_test - mean(label_test))^2;
% disp(['训练集数据的R2为：', num2str(R1)])
disp(['测试集数据的R2为：', num2str(R2)])

% mae1 = sum(abs(T_sim1 - train_label)) ./ train_length ;
mae2 = sum(abs(T_sim2 - label_test)) ./ total_test_length ;
% disp(['训练集数据的MAE为：', num2str(mae1)])
disp(['测试集数据的MAE为：', num2str(mae2)])

% mbe1 = sum(T_sim1 - train_label) ./ train_length ;
mbe2 = sum(T_sim2 - label_test ) ./ total_test_length ;
% disp(['训练集数据的MBE为：', num2str(mbe1)])
disp(['测试集数据的MBE为：', num2str(mbe2)])

%准确率
acc = 100*sum(T_sim2 == label_test)/numel(label_test); 
% 错报故障率
err_report1 = 100*sum(T_sim2 > label_test)/total_test_length;
% 漏报故障率
err_report2 = 100*sum(T_sim2 < label_test)/total_test_length;

将小数变成百分数输出
percent_acc=strcat(num2str(acc),'%');
percent_err_report1=strcat(num2str(err_report1),'%');
percent_err_report2=strcat(num2str(err_report2),'%');

显示验证结果
disp(['准确率：', percent_acc,newline,'错报率：', percent_err_report1,newline,'漏报率：', percent_err_report2])

%***********************************************************************************%
%*************************************函数定义**************************************%
%***********************************************************************************%

读取原始数据，切割，特征提取
function [test_matrix_data,label_test,arc_test_seg_length,norm_test_seg_length] = dataPreProcess(path_arc,path_norm,average_width,feature_length)
    % %对信号进行fft变换，获取信号频率分布
    T=4e-6;                %采样周期
    Fs=1/T;                %采样频率
    wlen = 1024; 			%窗函数长度
    hop = 1024; 				%重叠长度
    win = hann(wlen, 'periodic');%窗函数,使用海宁窗    
    %% 定义测试数据路径

    path_arc_test = path_arc;      %arc数据文件夹地址
    path_norm_test = path_norm;  %normal数据文件夹地址
    
    %% 获取待分割的电流数据路径下面所有的.csv文件的信息
    %遍历ARC文件夹下的所有文件
    [arc_test_files] = scanDir(path_arc_test);
    arc_test_files_length = length(arc_test_files);
    
    %遍历Normal文件夹下的所有文件，并将文件保存下来
    [norm_test_files] = scanDir(path_norm_test);
    norm_test_files_length = length(norm_test_files);
    
    %定义矩阵保存每个电流数据文件能够分割成多少个seg的样本
    arc_test_segment=zeros(arc_test_files_length,1);
    norm_test_segment=zeros(norm_test_files_length,1);
    
    %% 根据ARC下的每个文件名路径，依次读取.csv文件并进行分割
    parfor k=1:arc_test_files_length
        %读取文件夹里的第k个csv文件，并将其保存在矩阵中，readtable('filename.csv')读取CSV文件，并将数据存储在一个表格中
        %csvread(filename)不推荐
        yk=readmatrix(arc_test_files(k));
        %第k个csv文件中数据的长度
        length_yk = length(yk);
        %yk可以分成多少个1024个数据
        arc_test_segment(k,1) = fix(length_yk/wlen);
    end
    
    %计算所有arc电流样本的数量
    arc_test_seg_length = sum(arc_test_segment(:,1));
    
    %定义元胞数组保存分割后的每一个arc数据文件名字
    arc_test_names=cell(arc_test_seg_length,1);
    
    %定义矩阵暂存分割后的arc电流数据
    arc_test_seg_data=zeros(arc_test_seg_length,feature_length);
    
    for k=1:arc_test_files_length
        %读取文件夹里的第k个csv文件，并将其保存在矩阵中，readtable('filename.csv')读取CSV文件，并将数据存储在一个表格中
        %csvread(filename)不推荐
        arc_test_csv=dir(arc_test_files(k));
        yk=readmatrix(arc_test_files(k));
        filename = arc_test_csv.name;
        halfname = filename(1:end-4);       %去除文件名中的.csv字符
        arc_test_counts=sum(arc_test_segment(1:k,1))-arc_test_segment(k,1);
        for i=1:arc_test_segment(k,1)
            yk_i = yk(1+(i-1)*wlen : i*wlen);
            yk_diff_i = diff(yk_i);
            yk_diff_i(1024) = 0;
            [S2, f1, t1] = mystft(yk_i, win, hop, Fs,average_width,feature_length);
            arc_test_seg_data(arc_test_counts+i,:)=S2;
        end
    end
    
    %Normal部分
    parfor k=1:norm_test_files_length
        %读取文件夹里的第k个csv文件，并将其保存在矩阵中，readtable('filename.csv')读取CSV文件，并将数据存储在一个表格中
        %csvread(filename)不推荐
        yk=readmatrix(norm_test_files(k));
        %第k个csv文件中数据的长度
        length_yk = length(yk);
        %yk可以分成多少个seg个数据
        norm_test_segment(k,1) = fix(length_yk/wlen);
    end
    
    %计算所有norm电流样本的数量
    norm_test_seg_length = sum(norm_test_segment(:,1));

    
    %定义矩阵保存分割后的norm电流数据
    norm_test_seg_data=zeros(norm_test_seg_length,feature_length);
    
    for k=1:norm_test_files_length
        %读取文件夹里的第k个csv文件，并将其保存在矩阵中，readtable('filename.csv')读取CSV文件，并将数据存储在一个表格中
        %csvread(filename)不推荐
        yk=readmatrix(norm_test_files(k));
        norm_test_counts=sum(norm_test_segment(1:k,1))-norm_test_segment(k,1);    
        for i=1:norm_test_segment(k,1)
            yk_i = yk(1+(i-1)*wlen : i*wlen);
            yk_diff_i = diff(yk_i);
            yk_diff_i(1024) = 0;
            [S2, f1, t1] = mystft(yk_i, win, hop, Fs,average_width,feature_length);
            norm_test_seg_data(norm_test_counts+i,:)=S2;
        end
    end
    %显示当前日期和时间
    currentDateTime = datetime('now');
    fprintf('%s ', currentDateTime);
    
    
    %% 计算所有的单独训练样本集的数量
    total_test_length=arc_test_seg_length+norm_test_seg_length;
    
    %% 依次获取每一个.csv文件的二维数据，保存在matrix_data元胞矩阵里
    %定义元胞矩阵
    test_matrix_data=zeros(total_test_length,feature_length);
    
    %% 将.csv文件数据保存在元胞数组中
    for k=1:arc_test_seg_length
        test_matrix_data(k,:)=arc_test_seg_data(k,:);
    end
    for k=(arc_test_seg_length+1):total_test_length
        test_matrix_data(k,:)=norm_test_seg_data(k-arc_test_seg_length,:);
    end
    
    %% 创建标签
    label_test = zeros(total_test_length,1);
    for i=1:arc_test_seg_length
        label_test(i,1)=1;
    end
    for i=(arc_test_seg_length+1):total_test_length
        label_test(i,1)=0;
    end
end


scanDir函数定义
%%函数：scanDir 遍历总文件夹下的所有文件
%函数输入：root_dir 主目录文件夹
%输出：所有的包含路径的文件名

function [ str_matrix ] = scanDir( root_dir )  
      
    files={};  
    if root_dir(end)~='/'  
     root_dir=[root_dir,'/'];  
    end  
    fileList=dir(root_dir);  %扩展名  
    n=length(fileList);  
    cntpic=0;  
    for i=1:n  
        if strcmp(fileList(i).name,'.')==1||strcmp(fileList(i).name,'..')==1  
            continue;  
        else  
            fileList(i).name;
            if ~fileList(i).isdir  
                  
                full_name=[root_dir,fileList(i).name];  
                  
    %              [pathstr,name,ext,versn]=fileparts(full_name);  
    %              if strcmp(ext,'.jpg')  
                     cntpic=cntpic+1;  
                     files(cntpic)={full_name};  
    %              end  
            else  
                files=[files,scanDir([root_dir,fileList(i).name])];  
            end  
        end  
    end  
     % 获取输入矩阵的大小
    [m, n] = size(files);
    
    % 初始化输出矩阵
    str_matrix = strings(m, n);
    
    % 遍历输入矩阵的每个元素，将其转换为字符串并存储到输出矩阵中
    for i = 1:m
        for j = 1:n
            % 将单个元素转换为字符串，并将其存储到对应位置的输出矩阵中
            str_matrix(i,j) = string(files{i,j});
        end
    end
end

mystft函数定义
%%函数：mystft 用于短时傅里叶变换
%函数输入：x为输入数据，win为窗口大小，hop为间隙，fs为频率
%输出：STFT为短时傅里叶变换后的数据，f为频率坐标，t为时间坐标
function [result, f, t] = mystft(x, win, hop, fs,average_width,feature_length)
    xlen = length(x); 
    wlen = length(win);
    FFT = zeros(512);%返回空矩阵
    %进行快速傅里叶变换
    xw = x(1 : wlen).*win;
    X = fft(xw,wlen); % 执行 FFT
    X11 = abs(X);%求模
    X1=log10(abs(X));% 计算幅度的对数
    X2 = X1(1:wlen/2);% 提取一半的频谱
    A2=abs(X2)/wlen; % 归一化幅度
    A1=A2(1:wlen/2);
    A1(2:end-1)=2*A1(2:end-1);%将中间元素乘以2以改变信号的幅度
    FFT(:,1) = A1(1:512);
    data_length = length(FFT);
    truncated_length = floor(data_length / average_width) * average_width; % 计算可以被整除的长度
    truncated_data = FFT(1:truncated_length); % 截断数据
    % 将数据重新排列成10xN的矩阵
    reshaped_data = reshape(truncated_data, average_width, []);
    % 对每一列求平均值
    averages = mean(reshaped_data);
    % 创建一个feature_lengthx1的矩阵
    result = zeros(feature_length, 1);
    result(1:feature_length) = averages;
    min_value = min(result);
    result = result - min_value;
    
    %频率坐标
    f=fs*(0:(wlen/2))/wlen;
    %时间坐标
    t = (wlen/2:hop:wlen/2)/fs;%秒
end

