"""
演示使用data_pre_process方法的FFT可视化功能
这个脚本展示如何使用新的可视化功能来分析AFCI数据
"""
import os
import sys
import numpy as np
import time

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_demo_data():
    """
    创建演示数据
    """
    print("创建演示数据...")
    
    # 创建演示目录
    demo_dir = "demo_data"
    arc_dir = os.path.join(demo_dir, "ARC")
    nor_dir = os.path.join(demo_dir, "NOR")
    
    os.makedirs(arc_dir, exist_ok=True)
    os.makedirs(nor_dir, exist_ok=True)
    
    # 参数设置
    fs = 250000  # 采样频率 250kHz
    duration = 0.3  # 0.3秒的数据，确保有足够的数据点进行分段
    t = np.linspace(0, duration, int(fs * duration))
    
    print(f"生成数据参数:")
    print(f"  采样频率: {fs} Hz")
    print(f"  数据时长: {duration} 秒")
    print(f"  数据点数: {len(t)}")
    
    # 生成多个文件，模拟不同类型的故障和正常信号
    for i in range(5):
        # ARC信号：模拟电弧故障
        # 基础50Hz信号 + 高频故障成分 + 随机噪声
        base_signal = np.sin(2 * np.pi * 50 * t)
        
        # 不同类型的故障特征
        if i == 0:
            # 高频故障
            fault_signal = 0.3 * np.sin(2 * np.pi * 2000 * t)
        elif i == 1:
            # 中频故障
            fault_signal = 0.4 * np.sin(2 * np.pi * 800 * t)
        elif i == 2:
            # 多频故障
            fault_signal = (0.2 * np.sin(2 * np.pi * 1500 * t) + 
                           0.2 * np.sin(2 * np.pi * 3000 * t))
        elif i == 3:
            # 脉冲型故障
            pulse_freq = 100
            pulse_signal = np.zeros_like(t)
            pulse_indices = np.where(np.sin(2 * np.pi * pulse_freq * t) > 0.8)[0]
            pulse_signal[pulse_indices] = 0.5
            fault_signal = pulse_signal
        else:
            # 宽带噪声型故障
            fault_signal = 0.3 * np.random.randn(len(t))
        
        # 组合信号
        arc_signal = base_signal + fault_signal + 0.1 * np.random.randn(len(t))
        
        # Normal信号：只有基础信号和少量噪声
        normal_signal = base_signal + 0.05 * np.random.randn(len(t))
        
        # 保存数据
        np.savetxt(os.path.join(arc_dir, f"arc_fault_type_{i+1}.csv"), arc_signal, delimiter=',')
        np.savetxt(os.path.join(nor_dir, f"normal_signal_{i+1}.csv"), normal_signal, delimiter=',')
    
    print(f"演示数据已创建在 {demo_dir} 目录下")
    print(f"生成了 {5} 个ARC文件和 {5} 个Normal文件")
    return demo_dir

def run_demo():
    """
    运行演示
    """
    print("=" * 60)
    print("FFT可视化演示 - 使用data_pre_process方法")
    print("=" * 60)
    
    # 创建演示数据
    demo_dir = create_demo_data()
    
    try:
        # 导入可视化模块
        from fft_visualization import data_pre_process, visualize_processed_features
        
        # 设置路径
        arc_path = os.path.join(demo_dir, "ARC")
        norm_path = os.path.join(demo_dir, "NOR")
        output_dir = "demo_results/fft_vis_processed"
        
        # 参数设置（与fft_random_forest.py保持一致）
        average_width = 5
        feature_length = int(512 / average_width)
        
        print(f"\n开始数据处理...")
        print(f"ARC数据路径: {arc_path}")
        print(f"Normal数据路径: {norm_path}")
        print(f"输出目录: {output_dir}")
        print(f"平均窗口宽度: {average_width}")
        print(f"特征长度: {feature_length}")
        
        start_time = time.time()
        
        # 使用data_pre_process处理数据
        feature_matrix, labels, arc_length, norm_length = data_pre_process(
            arc_path, norm_path, average_width, feature_length
        )
        
        processing_time = time.time() - start_time
        
        print(f"\n数据处理完成 (耗时: {processing_time:.2f}秒):")
        print(f"  特征矩阵形状: {feature_matrix.shape}")
        print(f"  故障样本数: {arc_length}")
        print(f"  正常样本数: {norm_length}")
        print(f"  总样本数: {len(labels)}")
        
        # 显示一些统计信息
        arc_features = feature_matrix[:arc_length]
        norm_features = feature_matrix[arc_length:]
        
        print(f"\n特征统计信息:")
        print(f"  ARC特征均值范围: {np.mean(arc_features, axis=0).min():.4f} - {np.mean(arc_features, axis=0).max():.4f}")
        print(f"  Normal特征均值范围: {np.mean(norm_features, axis=0).min():.4f} - {np.mean(norm_features, axis=0).max():.4f}")
        print(f"  特征差异最大值: {np.max(np.abs(np.mean(arc_features, axis=0) - np.mean(norm_features, axis=0))):.4f}")
        
        # 生成可视化
        print(f"\n开始生成可视化图表...")
        vis_start_time = time.time()
        
        visualize_processed_features(
            feature_matrix, labels, arc_length, norm_length, output_dir, average_width
        )
        
        vis_time = time.time() - vis_start_time
        
        print(f"可视化完成 (耗时: {vis_time:.2f}秒)!")
        
        # 检查输出文件
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            print(f"\n生成的可视化文件 ({len(files)} 个):")
            for file in files:
                file_path = os.path.join(output_dir, file)
                file_size = os.path.getsize(file_path) / 1024  # KB
                print(f"  ✓ {file} ({file_size:.1f} KB)")
        
        print(f"\n演示完成！")
        print(f"总耗时: {time.time() - start_time:.2f}秒")
        print(f"结果保存在: {output_dir}")
        
        # 提供查看建议
        print(f"\n建议查看的图表:")
        print(f"  1. feature_distribution_comparison.png - 查看故障与正常数据的特征分布差异")
        print(f"  2. feature_frequency_comparison.png - 查看频率域的特征对比")
        print(f"  3. pca_visualization.png - 查看数据的聚类效果")
        print(f"  4. feature_heatmap.png - 查看特征的热力图分布")
        print(f"  5. sample_feature_curves.png - 查看典型样本的特征曲线")
        
        return True
        
    except Exception as e:
        print(f"\n演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理演示数据
        print(f"\n清理演示数据...")
        import shutil
        if os.path.exists(demo_dir):
            shutil.rmtree(demo_dir)
            print(f"已删除临时目录: {demo_dir}")

def show_usage():
    """
    显示使用说明
    """
    print("=" * 60)
    print("FFT可视化工具使用说明")
    print("=" * 60)
    print()
    print("1. 运行演示:")
    print("   python demo_processed_vis.py")
    print()
    print("2. 使用实际数据:")
    print("   python fft_visualization.py")
    print()
    print("3. 运行测试:")
    print("   python test_processed_vis.py")
    print()
    print("4. 查看原始数据可视化:")
    print("   python -c \"from fft_visualization import main_original; main_original()\"")
    print()
    print("更多信息请查看 README_FFT_VIS.md")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        show_usage()
    else:
        success = run_demo()
        if success:
            print("\n🎉 演示成功完成！")
        else:
            print("\n❌ 演示失败，请检查错误信息")
